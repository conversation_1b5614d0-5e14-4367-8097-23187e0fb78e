/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.service.DistributionConfigService;
import com.joolun.cloud.mall.api.service.DistributionUserService;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import com.joolun.cloud.mall.common.entity.DistributionUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 分销员
 *
 * <AUTHOR>
 * @date 2021-04-25 17:40:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/distributionconfig")
@Api(value = "distributionconfig", tags = "分销设置API")
public class DistributionConfigController {

	private final DistributionConfigService distributionConfigService;

	/**
	 * 查询积分设置
	 * @return R
	 */
	@ApiOperation(value = "查询分销设置")
	@GetMapping()
	public R get() {
		return R.ok(distributionConfigService.getOne(Wrappers.emptyWrapper()));
	}

}
