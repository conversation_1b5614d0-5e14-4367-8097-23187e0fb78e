/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import com.joolun.cloud.mall.common.entity.UserFootprint;
import com.joolun.cloud.mall.api.service.UserFootprintService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户足迹
 *
 * <AUTHOR>
 * @date 2020-12-24 22:09:03
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/userfootprint")
@Api(value = "userfootprint", tags = "用户足迹API")
public class UserFootprintController {

    private final UserFootprintService userFootprintService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param userFootprint 用户足迹
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
	@ApiLogin
    public R getPage(Page page, UserFootprint userFootprint) {
		userFootprint.setUserId(ThirdSessionHolder.getUserId());
        return R.ok(userFootprintService.page2(page, userFootprint));
    }

}
