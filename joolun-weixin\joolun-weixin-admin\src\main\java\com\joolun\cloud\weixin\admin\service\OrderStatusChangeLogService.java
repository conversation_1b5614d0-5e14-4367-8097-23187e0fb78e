package com.joolun.cloud.weixin.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.cloud.weixin.common.entity.OrderStatusChangeLog;

/**
 * 订单状态变更日志
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface OrderStatusChangeLogService extends IService<OrderStatusChangeLog> {

    /**
     * 记录订单状态变更日志
     *
     * @param orderId 订单ID
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param changeReason 变更原因
     */
    void logStatusChange(String orderId, String oldStatus, String newStatus, String changeReason);
}
