/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.common.feign.FeignWxAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 微信应用
 *
 * <AUTHOR>
 * @date 2019-08-25 15:39:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wxapp")
@Api(value = "wxapp", tags = "微信应用API")
public class WxAppController {

	private final FeignWxAppService feignWxAppService;

	/**
	 * 通过id查询微信应用配置
	 *
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "通过id查询微信应用配置")
	@GetMapping("/{id}")
	public R getById(@PathVariable("id") String id) {
		return R.ok(feignWxAppService.getById(id, SecurityConstants.FROM_IN));
	}
}
