<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.UserShopMapper">

    <resultMap id="userShopMap" type="com.joolun.cloud.mall.common.entity.UserShop">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
		<result property="userCode" column="user_code"/>
        <result property="shopId" column="shop_id"/>
		<result property="userId" column="user_id"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

	<resultMap id="userShopMap1" extends="userShopMap" type="com.joolun.cloud.mall.common.entity.UserShop">
		<collection property="userInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=user_id}">
		</collection>
	</resultMap>

    <sql id="userShopSql">
        user_shop.`id`,
        user_shop.`tenant_id`,
        user_shop.`del_flag`,
        user_shop.`create_time`,
        user_shop.`update_time`,
        user_shop.`user_code`,
        user_shop.`shop_id`,
        user_shop.`user_id`,
        user_shop.`remarks`
    </sql>

	<select id="selectPage1" resultMap="userShopMap1">
		SELECT
		<include refid="userShopSql"/>
		FROM user_shop user_shop
		<where>
			<if test="query.userId != null">
				AND user_shop.`user_id` = #{query.userId}
			</if>
			<if test="query.shopId != null">
				AND user_shop.`shop_id` = #{query.shopId}
			</if>
			<if test="query.userCode != null">
				AND user_shop.`user_code` = #{query.userCode}
			</if>
		</where>
	</select>
</mapper>
