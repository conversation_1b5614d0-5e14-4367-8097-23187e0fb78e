/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.common.nacos.util.NacosConfigUtils;
import com.joolun.cloud.mall.common.config.MallConfigProperties;
import com.joolun.cloud.mall.common.entity.Logistics;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.yaml.snakeyaml.Yaml;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物流公司
 *
 * <AUTHOR>
 * @date 2019-09-16 09:53:17
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/logistics")
@Api(value = "logistics", tags = "物流公司管理")
public class LogisticsController {

	private final MallConfigProperties mallConfigProperties;

	private NacosConfigUtils nacosConfigUtils;

	/**
	 * 物流公司列表
	 * @param
	 * @return
	 */
	@ApiOperation(value = "物流公司列表")
	@PreAuthorize("@ato.hasAuthority('mall:logistics:index')")
	@GetMapping("/page")
	public R getPage() {
		List<Logistics> listLogistics = mallConfigProperties.getLogistics();
		Page page = new Page();
		if(listLogistics != null && listLogistics.size() > 0){
			page.setRecords(listLogistics);
			page.setTotal(listLogistics.size());
		}
		return R.ok(page);
	}

	/**
	 * 物流公司列表
	 * @param
	 * @return
	 */
	@ApiOperation(value = "物流公司列表")
	@GetMapping("/list")
	public R getList() {
		return R.ok(mallConfigProperties.getLogistics());
	}

	/**
	 * 物流公司修改
	 * @param
	 * @return R
	 */
	@ApiOperation(value = "物流公司修改")
	@SysLog("物流公司修改")
	@PutMapping
	@PreAuthorize("@ato.hasAuthority('mall:logistics:edit')")
	public R update(@Valid @RequestBody Logistics logistics) throws NacosException, InterruptedException {
		ConfigService configService = nacosConfigUtils.getConfigService();
		String content = configService.getConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP, CommonConstants.CONFIG_TIMEOUT_MS);
		Yaml yaml = new Yaml();
		Map<String, Map<String, Map>> confMap= yaml.load(content);
		List<Map<String, String>> listLogistics = (List<Map<String, String>>) confMap.get("base").get("mall").get("logistics");
		for(int i = 0; i<listLogistics.size(); i++){
			if(StrUtil.equals(listLogistics.get(i).get("code"), logistics.getCode())){
				listLogistics.get(i).put("name", logistics.getName());
				listLogistics.get(i).put("enable", logistics.getEnable());
			}
		}
		String confStr = yaml.dumpAsMap(confMap);
		boolean rs = configService.publishConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP,confStr,"yaml");
		Thread.sleep(3000);
		return R.ok(rs);
	}

	/**
	 * 物流公司新增
	 * @param
	 * @return R
	 */
	@ApiOperation(value = "物流公司新增")
	@SysLog("物流公司新增")
	@PostMapping
	@PreAuthorize("@ato.hasAuthority('mall:logistics:add')")
	public R add(@Valid @RequestBody Logistics logistics) throws NacosException, InterruptedException {
		ConfigService configService = nacosConfigUtils.getConfigService();
		String content = configService.getConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP, CommonConstants.CONFIG_TIMEOUT_MS);
		Yaml yaml = new Yaml();
		Map<String, Map<String, Map>> confMap= yaml.load(content);
		List<Map<String, String>> listLogistics = (List<Map<String, String>>) confMap.get("base").get("mall").get("logistics");
		Map<String, String> map = new HashMap<>();
		map.put("code", logistics.getCode());
		map.put("name", logistics.getName());
		map.put("enable", logistics.getEnable());
		listLogistics.add(map);
		String confStr = yaml.dumpAsMap(confMap);
		boolean rs = configService.publishConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP,confStr,"yaml");
		Thread.sleep(3000);
		return R.ok(rs);
	}

	/**
	 * 删除物流公司
	 * @param code
	 * @return R
	 */
	@ApiOperation(value = "删除物流公司")
	@SysLog("删除物流公司")
	@DeleteMapping("/{code}")
	@PreAuthorize("@ato.hasAuthority('mall:logistics:del')")
	public R removeByCode(@PathVariable String code) throws NacosException, InterruptedException {
		ConfigService configService = nacosConfigUtils.getConfigService();
		String content = configService.getConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP, CommonConstants.CONFIG_TIMEOUT_MS);
		Yaml yaml = new Yaml();
		Map<String, Map<String, Map>> confMap= yaml.load(content);
		List<Map<String, String>> listLogistics = (List<Map<String, String>>) confMap.get("base").get("mall").get("logistics");
		for(int i = 0; i<listLogistics.size(); i++){
			if(StrUtil.equals(listLogistics.get(i).get("code"), code)){
				listLogistics.remove(i);
			}
		}
		String confStr = yaml.dumpAsMap(confMap);
		boolean rs = configService.publishConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP,confStr,"yaml");
		Thread.sleep(3000);
		return R.ok(rs);
	}
}
