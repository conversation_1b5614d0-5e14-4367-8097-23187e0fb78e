/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.DistributionUser;
import com.joolun.cloud.mall.admin.service.DistributionUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 分销员
 *
 * <AUTHOR>
 * @date 2021-04-25 17:40:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/distributionuser")
@Api(value = "distributionuser", tags = "分销员管理")
public class DistributionUserController {

    private final DistributionUserService distributionUserService;

    /**
     * 分销员分页列表
     * @param page 分页对象
     * @param distributionUser 分销员
     * @return
     */
    @ApiOperation(value = "分销员分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:distributionuser:index')")
    public R getPage(Page page, DistributionUser distributionUser) {
        return R.ok(distributionUserService.page1(page, distributionUser));
    }

    /**
     * 分销员查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销员查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionuser:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(distributionUserService.getById(id));
    }

    /**
     * 分销员新增
     * @param distributionUser 分销员
     * @return R
     */
    @ApiOperation(value = "分销员新增")
    @SysLog("新增分销员")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionuser:add')")
    public R save(@RequestBody DistributionUser distributionUser) {
        return R.ok(distributionUserService.save(distributionUser));
    }

    /**
     * 分销员修改
     * @param distributionUser 分销员
     * @return R
     */
    @ApiOperation(value = "分销员修改")
    @SysLog("修改分销员")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionuser:edit')")
    public R updateById(@RequestBody DistributionUser distributionUser) {
        return R.ok(distributionUserService.updateById(distributionUser));
    }

    /**
     * 分销员删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销员删除")
    @SysLog("删除分销员")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionuser:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(distributionUserService.removeById(id));
    }

}
