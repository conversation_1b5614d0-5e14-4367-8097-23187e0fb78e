/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.util.ApiUtil;
import com.joolun.cloud.mall.common.feign.FeignWxJsSdkService;
import com.joolun.cloud.weixin.common.dto.WxJsApiDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * jssdk
 *
 * <AUTHOR>
 * @date 2019-10-25 15:39:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wxjssdk")
@Api(value = "wxjssdk", tags = "jssdkAPI")
public class WxJsSdkController {

	private final FeignWxJsSdkService feignWxJsSdkService;
	/**
	 * 获取jssdk config参数
	 * @param wxJsApiDTO
	 * @return
	 */
	@ApiOperation(value = "获取jssdk config参数")
	@PostMapping("/config")
	public R getConfigInfo(HttpServletRequest request, @RequestBody WxJsApiDTO wxJsApiDTO){
		wxJsApiDTO.setAppId(ApiUtil.getAppId(request));
		return feignWxJsSdkService.createJsapiSignature(wxJsApiDTO, SecurityConstants.FROM_IN);
	}
}
