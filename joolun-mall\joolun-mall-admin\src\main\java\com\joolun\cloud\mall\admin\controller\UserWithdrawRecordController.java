/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.admin.service.DistributionOrderService;
import com.joolun.cloud.mall.admin.service.DistributionUserService;
import com.joolun.cloud.mall.common.entity.UserWithdrawRecord;
import com.joolun.cloud.mall.admin.service.UserWithdrawRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 用户提现记录
 *
 * <AUTHOR>
 * @date 2021-05-10 14:20:02
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/userwithdrawrecord")
@Api(value = "userwithdrawrecord", tags = "用户提现记录管理")
public class UserWithdrawRecordController {

    private final UserWithdrawRecordService userWithdrawRecordService;
	private final DistributionUserService distributionUserService;
	private final DistributionOrderService distributionOrderService;

    /**
     * 用户提现记录分页列表
     * @param page 分页对象
     * @param userWithdrawRecord 用户提现记录
     * @return
     */
    @ApiOperation(value = "用户提现记录分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:userwithdrawrecord:index')")
    public R getPage(Page page, UserWithdrawRecord userWithdrawRecord) {
        return R.ok(userWithdrawRecordService.page1(page, userWithdrawRecord));
    }

    /**
     * 用户提现记录查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "用户提现记录查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:userwithdrawrecord:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(userWithdrawRecordService.getById(id));
    }

    /**
     * 用户提现记录新增
     * @param userWithdrawRecord 用户提现记录
     * @return R
     */
    @ApiOperation(value = "用户提现记录新增")
    @SysLog("新增用户提现记录")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:userwithdrawrecord:add')")
    public R save(@RequestBody UserWithdrawRecord userWithdrawRecord) {
        return R.ok(userWithdrawRecordService.save(userWithdrawRecord));
    }

    /**
     * 用户提现记录修改
     * @param userWithdrawRecord 用户提现记录
     * @return R
     */
    @ApiOperation(value = "用户提现记录修改")
    @SysLog("修改用户提现记录")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:userwithdrawrecord:edit')")
    public R updateById(@RequestBody UserWithdrawRecord userWithdrawRecord) {
		userWithdrawRecord.setStatus(null);
        return R.ok(userWithdrawRecordService.updateById(userWithdrawRecord));
    }

    /**
     * 用户提现记录删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "用户提现记录删除")
    @SysLog("删除用户提现记录")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:userwithdrawrecord:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(userWithdrawRecordService.removeById(id));
    }

	/**
	 * 用户提现记录状态修改
	 * @param userWithdrawRecord 用户提现记录
	 * @return R
	 */
	@ApiOperation(value = "用户提现记录状态修改")
	@SysLog("修改用户提现记录状态")
	@PutMapping("/status")
	@PreAuthorize("@ato.hasAuthority('mall:userwithdrawrecord:edit')")
	public R updateStatusById(@RequestBody UserWithdrawRecord userWithdrawRecord) {
		return userWithdrawRecordService.updateStatus(userWithdrawRecord);
	}
}
