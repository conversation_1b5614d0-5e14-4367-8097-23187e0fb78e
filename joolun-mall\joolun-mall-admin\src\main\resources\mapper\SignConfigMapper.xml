<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.SignConfigMapper">

    <resultMap id="signConfigMap" type="com.joolun.cloud.mall.common.entity.SignConfig">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sort" column="sort"/>
        <result property="name" column="name"/>
        <result property="posts" column="posts"/>
    </resultMap>

    <sql id="signConfigSql">
        sign_config.`id`,        sign_config.`tenant_id`,        sign_config.`del_flag`,        sign_config.`create_time`,        sign_config.`update_time`,        sign_config.`sort`,        sign_config.`name`,        sign_config.`posts`    </sql>
</mapper>
