<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.OrderInfoMapper">

	<resultMap id="orderInfoMap" type="com.joolun.cloud.mall.common.entity.OrderInfo">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="shopId" column="shop_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="appType" column="app_type"/>
		<result property="appId" column="app_id"/>
		<result property="userId" column="user_id"/>
		<result property="orderNo" column="order_no"/>
		<result property="salesPrice" column="sales_price"/>
		<result property="freightPrice" column="freight_price"/>
		<result property="paymentWay" column="payment_way"/>
		<result property="deliveryWay" column="delivery_way"/>
		<result property="paymentType" column="payment_type"/>
		<result property="tradeType" column="trade_type"/>
		<result property="paymentPrice" column="payment_price"/>
		<result property="paymentPoints" column="payment_points"/>
		<result property="paymentCouponPrice" column="payment_coupon_price"/>
		<result property="paymentPointsPrice" column="payment_points_price"/>
		<result property="paymentTime" column="payment_time"/>
		<result property="deliveryTime" column="delivery_time"/>
		<result property="receiverTime" column="receiver_time"/>
		<result property="closingTime" column="closing_time"/>
		<result property="logisticsId" column="logistics_id"/>
		<result property="transactionId" column="transaction_id"/>
		<result property="remark" column="remark"/>
		<result property="isPay" column="is_pay"/>
		<result property="name" column="name"/>
		<result property="status" column="status"/>
		<result property="appraisesStatus" column="appraises_status"/>
		<result property="userMessage" column="user_message"/>
		<result property="orderType" column="order_type"/>
		<result property="marketId" column="market_id"/>
		<result property="relationId" column="relation_id"/>
	</resultMap>

	<resultMap id="orderInfoMap2" extends="orderInfoMap" type="com.joolun.cloud.mall.common.entity.OrderInfo">
		<collection property="listOrderItem" ofType="com.joolun.cloud.mall.common.entity.OrderItem"
					select="com.joolun.cloud.mall.admin.mapper.OrderItemMapper.selectList2"
					column="{orderId=id}">
		</collection>
		<collection property="orderLogistics" ofType="com.joolun.cloud.mall.common.entity.OrderLogistics"
					select="com.joolun.cloud.mall.admin.mapper.OrderLogisticsMapper.selectById"
					column="{id=logistics_id}">
		</collection>
	</resultMap>

	<sql id="orderInfoSql">
		order_info.`id`,
		order_info.`tenant_id`,
		order_info.`shop_id`,
		order_info.`del_flag`,
		order_info.`create_time`,
		order_info.`update_time`,
		order_info.`app_type`,
		order_info.`app_id`,
		order_info.`user_id`,
		order_info.`order_no`,
		order_info.`sales_price`,
		order_info.`freight_price`,
		order_info.`payment_way`,
		order_info.`delivery_way`,
		order_info.`payment_type`,
		order_info.`trade_type`,
		order_info.`payment_price`,
		order_info.`payment_points`,
		order_info.`payment_coupon_price`,
		order_info.`payment_points_price`,
		order_info.`payment_time`,
		order_info.`delivery_time`,
		order_info.`receiver_time`,
		order_info.`closing_time`,
		order_info.`is_pay`,
		order_info.`name`,
		order_info.`status`,
		order_info.`appraises_status`,
		order_info.`user_message`,
		order_info.`logistics_id`,
		order_info.`transaction_id`,
		order_info.`remark`,
		order_info.`order_type`,
		order_info.`market_id`,
		order_info.`relation_id`
	</sql>

	<select id="selectPage1" resultMap="orderInfoMap2">
		SELECT
		<include refid="orderInfoSql"/>
		FROM order_info order_info
		<where>
			<if test="query.shopId != null">
				AND order_info.`shop_id` = #{query.shopId}
			</if>
			<if test="query.userId != null">
				AND order_info.`user_id` = #{query.userId}
			</if>
			<if test="query.status != null">
				<if test="query.status == '0'.toString()">
					AND order_info.`is_pay` = '0'
					AND order_info.`status` is null
				</if>
				<if test="query.status == '4'.toString()">
					AND order_info.`appraises_status` = '0'
					AND order_info.`status` = '3'
				</if>
				<if test="query.status != '0'.toString() and query.status != '4'.toString()">
					AND order_info.`status` = #{query.status}
				</if>
			</if>
			<if test="query.isPay != null">
				AND order_info.`is_pay` = #{query.isPay}
			</if>
			<if test="query.deliveryWay != null">
				AND order_info.`delivery_way` = #{query.deliveryWay}
			</if>
			<if test="query.orderNo != null">
				AND order_info.`order_no` = #{query.orderNo}
			</if>
			<if test="query.beginTime != null">
				AND order_info.`create_time` >= #{query.beginTime}
			</if>
			<if test="query.endTime != null">
				AND #{query.endTime} >= order_info.`create_time`
			</if>
			<if test="query.appType != null">
				AND order_info.`app_type` = #{query.appType}
			</if>
			<if test="query.orderType != null">
				AND order_info.`order_type` = #{query.orderType}
			</if>
			<if test="query.name != null">
				AND order_info.`name` LIKE CONCAT('%',#{query.name},'%')
			</if>
		</where>
	</select>

	<select id="sumPaymentPrice" resultType="java.math.BigDecimal">
		SELECT
			SUM(order_info.`payment_price`)
		FROM order_info order_info
		<where>
			<if test="query.shopId != null">
				AND order_info.`shop_id` = #{query.shopId}
			</if>
			<if test="query.userId != null">
				AND order_info.`user_id` = #{query.userId}
			</if>
			<if test="query.status != null">
				<if test="query.status == '0'.toString()">
					AND order_info.`is_pay` = '0'
					AND order_info.`status` is null
				</if>
				<if test="query.status == '4'.toString()">
					AND order_info.`appraises_status` = '0'
					AND order_info.`status` = '3'
				</if>
				<if test="query.status != '0'.toString() and query.status != '4'.toString()">
					AND order_info.`status` = #{query.status}
				</if>
			</if>
			<if test="query.isPay != null">
				AND order_info.`is_pay` = #{query.isPay}
			</if>
			<if test="query.deliveryWay != null">
				AND order_info.`delivery_way` = #{query.deliveryWay}
			</if>
			<if test="query.orderNo != null">
				AND order_info.`order_no` = #{query.orderNo}
			</if>
			<if test="query.beginTime != null">
				AND order_info.`create_time` >= #{query.beginTime}
			</if>
			<if test="query.endTime != null">
				AND #{query.endTime} >= order_info.`create_time`
			</if>
		</where>
	</select>
</mapper>
