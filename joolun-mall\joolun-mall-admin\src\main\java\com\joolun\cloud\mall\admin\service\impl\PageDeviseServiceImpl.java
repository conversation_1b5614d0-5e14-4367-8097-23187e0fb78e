/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CacheConstants;
import com.joolun.cloud.mall.common.entity.PageDevise;
import com.joolun.cloud.mall.admin.mapper.PageDeviseMapper;
import com.joolun.cloud.mall.admin.service.PageDeviseService;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;

/**
 * 页面设计表
 *
 * <AUTHOR>
 * @date 2020-09-22 10:44:06
 */
@Service
@AllArgsConstructor
public class PageDeviseServiceImpl extends ServiceImpl<PageDeviseMapper, PageDevise> implements PageDeviseService {

	private final RedisTemplate redisTemplate;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateById(PageDevise entity) {
		PageDevise pageDevise = baseMapper.selectById(entity.getId());
		baseMapper.updateById(entity);
		//清理缓存
		String key = StrUtil.format("{}:{}:{}:{}:{}",
				CacheConstants.MALL_PAGE_DEVISE_CACHE,
				pageDevise.getTenantId(),
				pageDevise.getPageType(),
				pageDevise.getClientType(),
				pageDevise.getShopId());
		redisTemplate.delete(key);
		return Boolean.TRUE;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeById(Serializable id) {
		PageDevise pageDevise = baseMapper.selectById(id);
		baseMapper.deleteById(id);
		//清理缓存
		String key = StrUtil.format("{}:{}:{}:{}:{}",
				CacheConstants.MALL_PAGE_DEVISE_CACHE,
				pageDevise.getTenantId(),
				pageDevise.getPageType(),
				pageDevise.getClientType(),
				pageDevise.getShopId());
		redisTemplate.delete(key);
		return Boolean.TRUE;
	}
}
