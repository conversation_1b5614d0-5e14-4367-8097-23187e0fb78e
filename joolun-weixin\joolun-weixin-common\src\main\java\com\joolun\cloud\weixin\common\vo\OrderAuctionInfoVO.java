package com.joolun.cloud.weixin.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单档期信息VO
 * 用于封装订单关联的档期预约信息
 *
 * <AUTHOR>
 * @date 2024-07-27
 */
@Data
@ApiModel(description = "订单档期信息")
public class OrderAuctionInfoVO {

    /**
     * 店铺ID
     */
    @ApiModelProperty(value = "店铺ID")
    private String shopId;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    /**
     * 影棚ID
     */
    @ApiModelProperty(value = "影棚ID")
    private String roomId;

    /**
     * 影棚名称
     */
    @ApiModelProperty(value = "影棚名称")
    private String roomName;

    /**
     * 档期开始时间
     */
    @ApiModelProperty(value = "档期开始时间")
    private LocalDateTime startTime;

    /**
     * 档期结束时间
     */
    @ApiModelProperty(value = "档期结束时间")
    private LocalDateTime endTime;

    /**
     * 档期ID
     */
    @ApiModelProperty(value = "档期ID")
    private String auctionId;
}
