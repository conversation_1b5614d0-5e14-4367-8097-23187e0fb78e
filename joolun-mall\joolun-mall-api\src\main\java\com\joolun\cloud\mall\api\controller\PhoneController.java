package com.joolun.cloud.mall.api.controller;

import com.aliyuncs.exceptions.ClientException;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.security.annotation.Inside;
import com.joolun.cloud.mall.api.service.PhoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2018/11/14
 * <p>
 * 手机验证码
 */
@RestController
@AllArgsConstructor
@RequestMapping("/phone")
@Api(value = "phone", tags = "手机验证码API")
public class PhoneController {
	private final PhoneService phoneService;

	/**
	 * 发送手机验证码
	 * @param phone
	 * @return
	 * @throws ClientException
	 */
	@ApiOperation(value = "发送手机验证码")
	@GetMapping("/code")
	public R sendSmsCode(@RequestParam(value = "phone") String phone, @RequestParam(value = "type") String type) throws ClientException {
		return phoneService.sendSmsCode(phone,type);
	}

}
