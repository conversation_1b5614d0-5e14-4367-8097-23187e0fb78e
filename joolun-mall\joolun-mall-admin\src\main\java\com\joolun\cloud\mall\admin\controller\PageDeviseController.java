/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.PageDevise;
import com.joolun.cloud.mall.admin.service.PageDeviseService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 页面设计表
 *
 * <AUTHOR>
 * @date 2020-09-22 10:44:06
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/pagedevise")
@Api(value = "pagedevise", tags = "页面设计表管理")
public class PageDeviseController {

    private final PageDeviseService pageDeviseService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param pageDevise 页面设计表
     * @return
	 * mall:pagedevise:index：商城首页权限
	 * mall:pagedevise2:index：店铺首页权限
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:pagedevise:index','mall:pagedevise2:index')")
    public R getPage(Page page, PageDevise pageDevise) {
        return R.ok(pageDeviseService.page(page, Wrappers.query(pageDevise)));
    }

    /**
     * 页面设计表查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "页面设计表查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:pagedevise:get','mall:pagedevise2:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(pageDeviseService.getById(id));
    }

    /**
     * 页面设计表新增
     * @param pageDevise 页面设计表
     * @return R
     */
    @ApiOperation(value = "页面设计表新增")
    @SysLog("新增页面设计表")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:pagedevise:add','mall:pagedevise2:add')")
    public R save(@RequestBody PageDevise pageDevise) {
		pageDeviseService.save(pageDevise);
        return R.ok(pageDevise);
    }

    /**
     * 页面设计表修改
     * @param pageDevise 页面设计表
     * @return R
     */
    @ApiOperation(value = "页面设计表修改")
    @SysLog("修改页面设计表")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:pagedevise:edit','mall:pagedevise2:edit')")
    public R updateById(@RequestBody PageDevise pageDevise) {
        return R.ok(pageDeviseService.updateById(pageDevise));
    }

    /**
     * 页面设计表删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "页面设计表删除")
    @SysLog("删除页面设计表")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:pagedevise:del','mall:pagedevise2:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(pageDeviseService.removeById(id));
    }

}
