package com.joolun.cloud.common.data.datascope.shop;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.security.entity.BaseUser;
import com.joolun.cloud.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <p>
 * 店铺ID拦截器
 */
@Slf4j
@AllArgsConstructor
public class ShopIdInterceptor implements InnerInterceptor {

	private final ShopScopeProperties shopScopeProperties;

	@Override
	public void beforeQuery(Executor executor, MappedStatement mappedStatement, Object parameter, RowBounds rowBounds,
							  ResultHandler resultHandler, BoundSql boundSql) {
		PluginUtils.MPBoundSql mPBoundSql = PluginUtils.mpBoundSql(boundSql);
		//查询数据权限配置
		List<ShopScopeMapper> mapperIds = shopScopeProperties.getMappers();
		Map<String, List<ShopScopeMapper>> mapShopScopeMapper =  mapperIds.stream().collect(Collectors.groupingBy(ShopScopeMapper::getValue));
		String mappedStatementMapper;
		//未配置店铺数据权限，直接放行
		if (mapperIds==null || mapperIds.size()<=0) {
			return;
		}else{
			//com.joolun.cloud.mall.admin.mapper.GoodsSpuMapper.selectPage1
			mappedStatementMapper = mappedStatement.getId();
			String[] strs = StrUtil.splitToArray(mappedStatementMapper,".");
			mappedStatementMapper = strs[strs.length-2];
			//没有配置当前mapper，直接放行
			if(!mapShopScopeMapper.containsKey(mappedStatementMapper)){
				return;
			}
		}

		BaseUser user = SecurityUtils.getUser();
		if (user == null) {
			//没有用户信息，直接放行
			return;
		}
		//非店铺管理员类型账号直接放行
		if(!StrUtil.equals(CommonConstants.USER_TYPE_2, user.getType())){
			return;
		}
		String originalSql = boundSql.getSql();

		String scopeName = mapShopScopeMapper.get(mappedStatementMapper).get(0).getColumn();
		//获取当前用户的店铺id
		String shopId = user.getShopId();
		String mappedStatementId = mappedStatement.getId();
		//过滤店铺查询
		if(StrUtil.containsAny(mappedStatementId,"selectCount")){
			//count查询过滤
			String w = "";
			if(StrUtil.containsAnyIgnoreCase(originalSql,"where")){
				w = " and ";
			}else{
				w = " where ";
			}
			originalSql = originalSql + w + scopeName + " = '" + shopId + "'";
		} else if(StrUtil.containsAny(mappedStatementId,"selectPage","selectList")){
			//selectPage、selectList查询过滤
			originalSql = "select * from (" + originalSql + ") temp_data_scope where temp_data_scope." + scopeName + " = '" + shopId + "'";
		} else{
			//直接放行
			return;
		}
		mPBoundSql.sql(originalSql);
		return;
	}

}
