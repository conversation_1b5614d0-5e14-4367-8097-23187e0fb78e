<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.PointsConfigMapper">

	<resultMap id="pointsConfigMap" type="com.joolun.cloud.mall.common.entity.PointsConfig">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="initPosts" column="init_posts"/>
		<result property="initVipPosts" column="init_vip_posts"/>
		<result property="premiseAmount" column="premise_amount"/>
		<result property="defaultDeductScale" column="default_deduct_scale"/>
		<result property="defaultDeductAmount" column="default_deduct_amount"/>
	</resultMap>
</mapper>
