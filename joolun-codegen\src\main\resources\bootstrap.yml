server:
  port: 7001

spring:
  application:
    name: @artifactId@
  #配置中心
  cloud:
    nacos:
      discovery:
        server-addr: joolun-nacos:8848
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs[0]:
          data-id: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          refresh: true
  profiles:
    active: dev