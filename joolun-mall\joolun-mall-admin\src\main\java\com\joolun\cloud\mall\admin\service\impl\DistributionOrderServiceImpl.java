/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.common.entity.DistributionOrder;
import com.joolun.cloud.mall.admin.mapper.DistributionOrderMapper;
import com.joolun.cloud.mall.admin.service.DistributionOrderService;
import com.joolun.cloud.mall.common.entity.DistributionUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 分销订单
 *
 * <AUTHOR>
 * @date 2021-04-30 10:26:30
 */
@Service
public class DistributionOrderServiceImpl extends ServiceImpl<DistributionOrderMapper, DistributionOrder> implements DistributionOrderService {

	@Override
	public IPage<DistributionOrder> page1(IPage<DistributionOrder> page, DistributionOrder distributionOrder) {
		return baseMapper.selectPage1(page, distributionOrder);
	}

	@Override
	public BigDecimal getDistributionUserFrozenAmount(String distributionUserId) {
		return baseMapper.getDistributionUserFrozenAmount(distributionUserId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCommission(String id, BigDecimal commission) {
		baseMapper.update(new DistributionOrder(), Wrappers.<DistributionOrder>lambdaUpdate()
				.eq(DistributionOrder::getId, id)
				.setSql(" commission = commission + " + commission));
	}
}
