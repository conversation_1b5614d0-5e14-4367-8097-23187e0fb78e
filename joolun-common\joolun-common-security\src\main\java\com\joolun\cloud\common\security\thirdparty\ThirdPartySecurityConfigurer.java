package com.joolun.cloud.common.security.thirdparty;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.joolun.cloud.common.security.component.ResourceAuthExceptionEntryPoint;
import com.joolun.cloud.common.security.service.BaseUserDetailsService;
import com.joolun.cloud.common.security.thirdparty.config.ThirdPartyConfigProperties;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationEventPublisher;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 手机号登录配置入口
 */
@Getter
@Setter
@Component
public class ThirdPartySecurityConfigurer extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {
	@Autowired
	private ObjectMapper objectMapper;
	@Autowired
	private AuthenticationEventPublisher defaultAuthenticationEventPublisher;
	private AuthenticationSuccessHandler loginSuccessHandler;
	private BaseUserDetailsService userDetailsService;
	private ThirdPartyConfigProperties thirdPartyConfigProperties;

	@Override
	public void configure(HttpSecurity http) {
		ThirdPartyAuthenticationFilter thirdPartyAuthenticationFilter = new ThirdPartyAuthenticationFilter();
		thirdPartyAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
		thirdPartyAuthenticationFilter.setAuthenticationSuccessHandler(loginSuccessHandler);
		thirdPartyAuthenticationFilter.setEventPublisher(defaultAuthenticationEventPublisher);
		thirdPartyAuthenticationFilter.setAuthenticationEntryPoint(new ResourceAuthExceptionEntryPoint(objectMapper));

		ThirdPartyAuthenticationProvider thirdPartyAuthenticationProvider = new ThirdPartyAuthenticationProvider();
		thirdPartyAuthenticationProvider.setUserDetailsService(userDetailsService);
		thirdPartyAuthenticationProvider.setThirdPartyConfigProperties(thirdPartyConfigProperties);
		http.authenticationProvider(thirdPartyAuthenticationProvider)
			.addFilterAfter(thirdPartyAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
	}
}
