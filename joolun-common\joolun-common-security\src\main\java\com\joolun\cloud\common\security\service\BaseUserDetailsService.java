package com.joolun.cloud.common.security.service;

import com.joolun.cloud.upms.common.entity.SysUser;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * <AUTHOR>
public interface BaseUserDetailsService extends UserDetailsService {

	/**
	 * 手机验证码登录
	 *
	 * @param code TYPE:CODE
	 * @return UserDetails
	 * @throws UsernameNotFoundException
	 */
	UserDetails loadUserByPhone(String code) throws UsernameNotFoundException;

	/**
	 * 第三方登录,openId登录
	 *
	 * @param openId
	 * @return UserDetails
	 * @throws UsernameNotFoundException
	 */
	UserDetails loadUserByThirdParty(String type, String openId) throws UsernameNotFoundException;
}
