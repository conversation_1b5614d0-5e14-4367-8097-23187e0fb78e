package com.joolun.cloud.auth.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.joolun.cloud.common.security.handler.PhoneLoginSuccessHandler;
import com.joolun.cloud.common.security.handler.ThirdPartyLoginSuccessHandler;
import com.joolun.cloud.common.security.phone.PhoneSecurityConfigurer;
import com.joolun.cloud.common.security.service.BaseUserDetailsService;
import com.joolun.cloud.common.security.thirdparty.ThirdPartySecurityConfigurer;
import com.joolun.cloud.common.security.thirdparty.config.ThirdPartyConfigProperties;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;

/**
 * <AUTHOR> 认证相关配置
 */
@Primary
@Order(90)
@Configuration
public class WebSecurityConfigurer extends WebSecurityConfigurerAdapter {
	@Autowired
	private ObjectMapper objectMapper;
	@Autowired
	private ClientDetailsService clientDetailsService;
	@Autowired
	private BaseUserDetailsService userDetailsService;
	@Autowired
	private ThirdPartyConfigProperties thirdPartyConfigProperties;
	@Lazy
	@Autowired
	private AuthorizationServerTokenServices defaultAuthorizationServerTokenServices;

	@Override
	@SneakyThrows
	protected void configure(HttpSecurity http) {
		http
			.formLogin()
			.loginPage("/token/login")
			.loginProcessingUrl("/token/form")
			.and()
			.authorizeRequests()
			.antMatchers(
				"/token/**",
				"/actuator/**",
				"/phone/**",
				"/thirdparty/**").permitAll()
			.anyRequest().authenticated()
			.and().csrf().disable()
			.apply(phoneSecurityConfigurer())
			.and().csrf().disable()
			.apply(thirdPartySecurityConfigurer());
	}

	/**
	 * 不拦截静态资源
	 *
	 * @param web
	 */
	@Override
	public void configure(WebSecurity web) {
		web.ignoring().antMatchers("/css/**");
	}

	@Bean
	@Override
	@SneakyThrows
	public AuthenticationManager authenticationManagerBean() {
		return super.authenticationManagerBean();
	}

	@Bean
	public AuthenticationSuccessHandler phoneLoginSuccessHandler() {
		return PhoneLoginSuccessHandler.builder()
			.objectMapper(objectMapper)
			.clientDetailsService(clientDetailsService)
			.passwordEncoder(passwordEncoder())
			.defaultAuthorizationServerTokenServices(defaultAuthorizationServerTokenServices).build();
	}

	@Bean
	public PhoneSecurityConfigurer phoneSecurityConfigurer() {
		PhoneSecurityConfigurer phoneSecurityConfigurer = new PhoneSecurityConfigurer();
		phoneSecurityConfigurer.setLoginSuccessHandler(phoneLoginSuccessHandler());
		phoneSecurityConfigurer.setUserDetailsService(userDetailsService);
		return phoneSecurityConfigurer;
	}

	@Bean
	public AuthenticationSuccessHandler thirdPartyLoginSuccessHandler() {
		return ThirdPartyLoginSuccessHandler.builder()
				.objectMapper(objectMapper)
				.clientDetailsService(clientDetailsService)
				.passwordEncoder(passwordEncoder())
				.defaultAuthorizationServerTokenServices(defaultAuthorizationServerTokenServices).build();
	}

	@Bean
	public ThirdPartySecurityConfigurer thirdPartySecurityConfigurer() {
		ThirdPartySecurityConfigurer thirdPartySecurityConfigurer = new ThirdPartySecurityConfigurer();
		thirdPartySecurityConfigurer.setLoginSuccessHandler(thirdPartyLoginSuccessHandler());
		thirdPartySecurityConfigurer.setUserDetailsService(userDetailsService);
		thirdPartySecurityConfigurer.setThirdPartyConfigProperties(thirdPartyConfigProperties);
		return thirdPartySecurityConfigurer;
	}

	/**
	 * https://spring.io/blog/2017/11/01/spring-security-5-0-0-rc1-released#password-storage-updated
	 * Encoded password does not look like BCrypt
	 *
	 * @return PasswordEncoder
	 */
	@Bean
	public PasswordEncoder passwordEncoder() {
		return PasswordEncoderFactories.createDelegatingPasswordEncoder();
	}

}
