# 使用说明
# 1. 使用docker-compose  不需要配置hosts
# 2. 根目录docker-compose up
# 3. 等待服务启动

version: '2'
services:
  joolun-redis:
    image: redis:5.0.4
    restart: always
    container_name: joolun-redis
    ports:
      - 6379:6379

  joolun-mysql:
    build:
      context: ./
      dockerfile: ./db/Dockerfile
    environment:
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: joolun-mysql
    image: joolun-mysql
    ports:
      - 3306:3306
    privileged: true
    volumes:
      - ./joolun-mysql:/var/lib/mysql
    command: --lower_case_table_names=1
    depends_on:
      - joolun-redis

  # 参考https://nacos.io/zh-cn/docs/quick-start-docker.html
  joolun-nacos:
    image: nacos/nacos-server:2.0.3
    restart: always
    container_name: joolun-nacos
    environment:
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=joolun-mysql
      - MYSQL_SERVICE_PORT=3306
      - MYSQL_SERVICE_DB_NAME=joolun_config
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=root
    ports:
      - 8848:8848
    depends_on:
      - joolun-mysql

  joolun-gateway:
    build:
      context: ./
      dockerfile: ./Dockerfile-gateway
    restart: always
    container_name: joolun-gateway
    image: joolun-gateway
    ports:
      - 9999:9999
    depends_on:
      - joolun-nacos

  joolun-auth:
    build:
      context: ./
      dockerfile: ./Dockerfile-auth
    restart: always
    container_name: joolun-auth
    image: joolun-auth
    depends_on:
      - joolun-nacos

  joolun-upms-admin:
    build:
      context: ./
      dockerfile: ./Dockerfile-upms-admin
    restart: always
    container_name: joolun-upms-admin
    image: joolun-upms-admin
    depends_on:
      - joolun-nacos

  joolun-weixin-admin:
    build:
      context: ./
      dockerfile: ./Dockerfile-weixin-admin
    restart: always
    container_name: joolun-weixin-admin
    image: joolun-weixin-admin
    depends_on:
      - joolun-nacos

  joolun-mall-admin:
    build:
      context: ./
      dockerfile: ./Dockerfile-mall-admin
    restart: always
    container_name: joolun-mall-admin
    image: joolun-mall-admin
    depends_on:
      - joolun-nacos

  joolun-mall-api:
    build:
      context: ./
      dockerfile: ./Dockerfile-mall-api
    restart: always
    container_name: joolun-mall-api
    image: joolun-mall-api
    depends_on:
      - joolun-nacos

  joolun-pay-api:
    build:
      context: ./
      dockerfile: ./Dockerfile-pay-api
    restart: always
    container_name: joolun-pay-api
    image: joolun-pay-api
    depends_on:
      - joolun-nacos

  joolun-codegen:
    build:
      context: ./
      dockerfile: ./Dockerfile-codegen
    restart: always
    image: joolun-codegen
    container_name: joolun-codegen
    depends_on:
      - joolun-nacos
