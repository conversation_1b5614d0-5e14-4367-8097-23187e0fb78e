/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.weixin.common.entity;

import lombok.Data;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;

/**
 * 支付配置
 *
 * <AUTHOR>
 * @date 2020-05-29 16:37:13
 */
@Data
@ApiModel(description = "支付配置")
public class PayConfig implements Serializable {
    private static final long serialVersionUID=1L;

    /**
     * PK
     */
    @NotNull(message = "PK不能为空")
    @ApiModelProperty(value = "PK")
    private String id;
    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updateId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除标记（0：显示；1：隐藏）
     */
    @ApiModelProperty(value = "逻辑删除标记（0：显示；1：隐藏）")
    private String delFlag;
    /**
     * 所属租户
     */
    @NotNull(message = "所属租户不能为空")
    @ApiModelProperty(value = "所属租户")
    private String tenantId;
    /**
     * 类型：1微信支付；2支付宝
     */
	@NotNull(message = "类型不能为空")
    @ApiModelProperty(value = "类型：1微信支付；2支付宝")
    private String type;
	/**
	 * 微信支付/支付宝服务商的APPID
	 */
	@NotNull(message = "服务商的APPID不能为空")
	@ApiModelProperty(value = "服务商的APPID")
	private String appId;
    /**
     * 微信支付商户号
     */
    @NotNull(message = "微信支付商户号不能为空")
    @ApiModelProperty(value = "微信支付商户号")
    private String mchId;
    /**
     * 微信支付商户密钥/支付宝私钥
     */
    @NotNull(message = "微信支付商户密钥/支付宝私钥不能为空")
    @ApiModelProperty(value = "微信支付商户密钥/支付宝私钥")
    private String mchKey;
    /**
     * 微信支付p12证书的文件路径/支付宝根证书文件路径
     */
    @NotNull(message = "微信支付p12证书的文件路径/支付宝根证书文件路径不能为空")
    @ApiModelProperty(value = "微信支付p12证书的文件路径/支付宝根证书文件路径")
    private String keyPath;
	/**
	 * 微信支付apiclient_key.pem证书的文件路径/支付宝公钥证书文件路径
	 */
	@NotNull(message = "微信支付apiclient_key.pem证书的文件路径/支付宝公钥证书文件路径不能为空")
	@ApiModelProperty(value = "微信支付apiclient_key.pem证书的文件路径/支付宝公钥证书文件路径")
	private String privateKeyPath;
	/**
	 * 微信支付apiclient_cert.pem证书的文件路径/支付宝应用公钥证书文件路径
	 */
	@NotNull(message = "微信支付apiclient_cert.pem证书的文件路径/支付宝应用公钥证书文件路径不能为空")
	@ApiModelProperty(value = "微信支付apiclient_cert.pem证书的文件路径/支付宝应用公钥证书文件路径")
	private String privateCertPath;
	/**
	 * 微信支付apiV3证书序列号
	 */
	@NotNull(message = "微信支付apiV3证书序列号不能为空")
	@ApiModelProperty(value = "微信支付apiV3证书序列号")
	private String certSerialNo;
	/**
	 * 微信支付apiV3 秘钥
	 */
	@NotNull(message = "微信支付apiV3 秘钥不能为空")
	@ApiModelProperty(value = "微信支付apiV3 秘钥")
	private String apiV3Key;
	/**
	 * 微信开放平台上申请的移动应用APPID
	 */
	@NotNull(message = "微信开放平台上申请的移动应用APPID不能为空")
	@ApiModelProperty(value = "微信开放平台上申请的移动应用APPID")
	private String subAppId;
}
