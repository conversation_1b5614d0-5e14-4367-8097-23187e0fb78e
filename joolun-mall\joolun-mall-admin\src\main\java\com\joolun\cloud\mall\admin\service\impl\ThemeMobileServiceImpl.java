/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CacheConstants;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.mall.common.entity.ThemeMobile;
import com.joolun.cloud.mall.admin.mapper.ThemeMobileMapper;
import com.joolun.cloud.mall.admin.service.ThemeMobileService;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * 移动端主题配置
 *
 * <AUTHOR>
 * @date 2020-06-04 13:49:31
 */
@Service
@AllArgsConstructor
public class ThemeMobileServiceImpl extends ServiceImpl<ThemeMobileMapper, ThemeMobile> implements ThemeMobileService {

	private final RedisTemplate redisTemplate;

	@Override
	public boolean updateById(ThemeMobile entity) {
		baseMapper.updateById(entity);
		//清缓存
		redisTemplate.delete(getRedisKey());
		return Boolean.TRUE;
	}

	@Override
	public boolean removeById(Serializable id) {
		baseMapper.deleteById(id);
		//清缓存
		redisTemplate.delete(getRedisKey());
		return Boolean.TRUE;
	}

	String getRedisKey(){
		return StrUtil.format("{}::{}", CacheConstants.MALL_THEME_MOBILE_CACHE, TenantContextHolder.getTenantId());
	}
}
