/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joolun.cloud.mall.common.entity.ShopInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 店铺表
 *
 * <AUTHOR>
 * @date 2020-05-15 14:35:53
 */
public interface ShopInfoMapper extends BaseMapper<ShopInfo> {

	/**
	 * 分布查询带商品
	 * @param page
	 * @param shopInfo
	 * @return
	 */
	IPage<ShopInfo> selectPageWithSpu(IPage<ShopInfo> page, @Param("query") ShopInfo shopInfo);

}
