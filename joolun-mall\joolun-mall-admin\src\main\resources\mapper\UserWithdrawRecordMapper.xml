<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.UserWithdrawRecordMapper">

    <resultMap id="userWithdrawRecordMap" type="com.joolun.cloud.mall.common.entity.UserWithdrawRecord">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="withdrawType" column="withdraw_type"/>
        <result property="applyAmount" column="apply_amount"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="paymentDetail" column="payment_detail"/>
        <result property="status" column="status"/>
        <result property="verifyDetail" column="verify_detail"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

	<resultMap id="userWithdrawRecordMap1" extends="userWithdrawRecordMap" type="com.joolun.cloud.mall.common.entity.UserWithdrawRecord">
		<collection property="userInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=user_id}">
		</collection>
	</resultMap>

    <sql id="userWithdrawRecordSql">
        user_withdraw_record.`id`,
        user_withdraw_record.`tenant_id`,
        user_withdraw_record.`del_flag`,
        user_withdraw_record.`create_time`,
        user_withdraw_record.`update_time`,
        user_withdraw_record.`user_id`,
        user_withdraw_record.`withdraw_type`,
        user_withdraw_record.`apply_amount`,
        user_withdraw_record.`payment_method`,
        user_withdraw_record.`payment_detail`,
        user_withdraw_record.`status`,
        user_withdraw_record.`verify_detail`,
        user_withdraw_record.`remarks`
    </sql>

	<select id="selectPage1" resultMap="userWithdrawRecordMap1">
		SELECT
		<include refid="userWithdrawRecordSql"/>
		FROM user_withdraw_record user_withdraw_record
		<where>
			<if test="query.userId != null">
				AND user_withdraw_record.`user_id` = #{query.userId}
			</if>
			<if test="query.withdrawType != null">
				AND user_withdraw_record.`withdraw_type` = #{query.withdrawType}
			</if>
			<if test="query.paymentMethod != null">
				AND user_withdraw_record.`payment_method` = #{query.paymentMethod}
			</if>
			<if test="query.status != null">
				AND user_withdraw_record.`status` = #{query.status}
			</if>
		</where>
	</select>
</mapper>
