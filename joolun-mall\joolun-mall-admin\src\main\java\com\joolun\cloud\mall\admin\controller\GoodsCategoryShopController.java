/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.GoodsCategoryShop;
import com.joolun.cloud.mall.admin.service.GoodsCategoryShopService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 店铺商品分类表
 *
 * <AUTHOR>
 * @date 2020-08-26 08:58:11
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodscategoryshop")
@Api(value = "goodscategoryshop", tags = "店铺商品分类表管理")
public class GoodsCategoryShopController {

    private final GoodsCategoryShopService goodsCategoryShopService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param goodsCategoryShop 店铺商品分类表
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:goodscategoryshop:index')")
    public R getPage(Page page, GoodsCategoryShop goodsCategoryShop) {
        return R.ok(goodsCategoryShopService.page(page, Wrappers.query(goodsCategoryShop)));
    }

	/**
	 *  返回树形集合
	 * @return
	 */
	@ApiOperation(value = "返回树形集合")
	@GetMapping("/tree")
	public R getTree(GoodsCategoryShop goodsCategoryShop) {
		return R.ok(goodsCategoryShopService.selectTree(goodsCategoryShop));
	}

    /**
     * 店铺商品分类表查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺商品分类表查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodscategoryshop:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(goodsCategoryShopService.getById(id));
    }

    /**
     * 店铺商品分类表新增
     * @param goodsCategoryShop 店铺商品分类表
     * @return R
     */
    @ApiOperation(value = "店铺商品分类表新增")
    @SysLog("新增店铺商品分类表")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodscategoryshop:add')")
    public R save(@RequestBody GoodsCategoryShop goodsCategoryShop) {
        return R.ok(goodsCategoryShopService.save(goodsCategoryShop));
    }

    /**
     * 店铺商品分类表修改
     * @param goodsCategoryShop 店铺商品分类表
     * @return R
     */
    @ApiOperation(value = "店铺商品分类表修改")
    @SysLog("修改店铺商品分类表")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodscategoryshop:edit')")
    public R updateById(@RequestBody GoodsCategoryShop goodsCategoryShop) {
        return R.ok(goodsCategoryShopService.updateById(goodsCategoryShop));
    }

    /**
     * 店铺商品分类表删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺商品分类表删除")
    @SysLog("删除店铺商品分类表")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodscategoryshop:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(goodsCategoryShopService.removeById(id));
    }

}
