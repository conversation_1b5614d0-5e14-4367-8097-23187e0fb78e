package com.joolun.cloud.codegen;

import com.joolun.cloud.common.datasource.annotation.EnableDynamicDataSource;
import com.joolun.cloud.common.security.annotation.EnableBaseResourceServer;
import com.joolun.cloud.common.security.annotation.EnableBaseFeignClients;
import com.joolun.cloud.common.swagger.annotation.BaseEnableSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @date 2018/07/29
 * 代码生成模块
 */
@EnableDynamicDataSource
@BaseEnableSwagger
@SpringBootApplication
@EnableBaseFeignClients
@EnableBaseResourceServer
public class JooLunCodeGenApplication {

	public static void main(String[] args) {
		SpringApplication.run(JooLunCodeGenApplication.class, args);
	}
}
