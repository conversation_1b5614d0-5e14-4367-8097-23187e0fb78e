/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.common.entity.SignRecord;
import com.joolun.cloud.mall.admin.mapper.SignRecordMapper;
import com.joolun.cloud.mall.admin.service.SignRecordService;
import com.joolun.cloud.mall.common.entity.UserCollect;
import org.springframework.stereotype.Service;

/**
 * 签到记录
 *
 * <AUTHOR>
 * @date 2021-01-13 15:29:30
 */
@Service
public class SignRecordServiceImpl extends ServiceImpl<SignRecordMapper, SignRecord> implements SignRecordService {

	@Override
	public IPage<SignRecord> page1(IPage<SignRecord> page, SignRecord signRecord) {
		return baseMapper.selectPage1(page,signRecord);
	}
}
