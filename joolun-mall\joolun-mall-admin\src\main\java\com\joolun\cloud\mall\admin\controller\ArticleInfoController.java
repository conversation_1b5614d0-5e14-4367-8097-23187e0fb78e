/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.ArticleInfo;
import com.joolun.cloud.mall.admin.service.ArticleInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 文章
 *
 * <AUTHOR>
 * @date 2020-11-24 14:44:15
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/articleinfo")
@Api(value = "articleinfo", tags = "文章管理")
public class ArticleInfoController {

    private final ArticleInfoService articleInfoService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param articleInfo 文章
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:articleinfo:index')")
    public R getPage(Page page, ArticleInfo articleInfo) {
        return R.ok(articleInfoService.page(page, Wrappers.query(articleInfo)));
    }

    /**
     * 文章查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "文章查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:articleinfo:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(articleInfoService.getById(id));
    }

    /**
     * 文章新增
     * @param articleInfo 文章
     * @return R
     */
    @ApiOperation(value = "文章新增")
    @SysLog("新增文章")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:articleinfo:add')")
    public R save(@RequestBody ArticleInfo articleInfo) {
        return R.ok(articleInfoService.save(articleInfo));
    }

    /**
     * 文章修改
     * @param articleInfo 文章
     * @return R
     */
    @ApiOperation(value = "文章修改")
    @SysLog("修改文章")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:articleinfo:edit')")
    public R updateById(@RequestBody ArticleInfo articleInfo) {
        return R.ok(articleInfoService.updateById(articleInfo));
    }

    /**
     * 文章删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "文章删除")
    @SysLog("删除文章")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:articleinfo:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(articleInfoService.removeById(id));
    }

}
