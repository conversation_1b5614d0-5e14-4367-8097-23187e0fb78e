package com.joolun.cloud.common.core.constant;

/**
 * <AUTHOR> <p>
 * 缓存的key 常量
 */
public interface CacheConstants {

	/**
	 * 用户信息缓存
	 */
	String USER_CACHE = "user_cache";

	/**
	 * oauth 客户端信息
	 */
	String OAUTH_CLIENT_CACHE = "base_oauth:client:cache";

	/**
	 * 菜单信息缓存
	 */
	String MENU_CACHE = "menu_cache";

	/**
	 * spring boot admin 事件key
	 */
	String EVENT_KEY = "event_key";

	/**
	 * 路由缓存
	 */
	String ROUTE_CACHE = "gateway_route_cache";

	/**
	 * 字典缓存
	 */
	String DICT_CACHE = "dict_cache";

	/**
	 * 默认验证码前缀
	 */
	String VER_CODE_DEFAULT = "ver_code_default:";

	/**
	 * 表单验证码前缀
	 */
	String VER_CODE_FORM = "ver_code_from:";
	/**
	 * 分享绑定验证码前缀
	 */
	String VER_CODE_SHARE = "ver_code_share:";

	/**
	 * 注册验证码前缀
	 */
	String VER_CODE_REGISTER = "ver_code_register:";

	/**
	 * wxapp缓存
	 */
	String WXAPP_WEIXIN_SIGN_CACHE = "wx:weixin_Sign";
	/**
	 * wxapp缓存
	 */
	String WXAPP_APP_ID_CACHE = "wx:app_id";
	/**
	 * shopInfo店铺缓存
	 */
	String MALL_SHOP_INFO_CACHE = "mall:shop_info";
	/**
	 * 页面设计缓存
	 */
	String MALL_PAGE_DEVISE_CACHE = "mall:page_devise";
	/**
	 * 微信页面设计缓存
	 */
	String WX_PAGE_DEVISE_CACHE = "wx:page_devise";
	/**
	 * 微信预览二维码缓存
	 */
	String WX_TEMP_QR_CODE= "wx:temp_qr_code";
	/**
	 * 支付配置缓存
	 */
	String PAY_CONFIC_APP_ID_CACHE = "pay:config:appid";
	/**
	 * 支付配置缓存
	 */
	String PAY_CONFIC_ID_CACHE = "pay:config:id";
	/**
	 * 移动端主题配置缓存
	 */
	String MALL_THEME_MOBILE_CACHE = "mall:theme_mobile";
}
