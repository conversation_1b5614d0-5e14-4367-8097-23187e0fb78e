<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.joolun</groupId>
		<artifactId>joolun-plus</artifactId>
		<version>1.0.16</version>
	</parent>

	<artifactId>joolun-common</artifactId>
	<packaging>pom</packaging>

	<description>公共聚合模块</description>

	<modules>
		<module>joolun-common-core</module>
		<module>joolun-common-data</module>
		<module>joolun-common-log</module>
		<module>joolun-common-security</module>
		<module>joolun-common-swagger</module>
		<module>joolun-common-datasource</module>
		<module>joolun-common-transaction</module>
		<module>joolun-common-storage</module>
		<module>joolun-common-email</module>
		<module>joolun-common-sms</module>
		<module>joolun-common-jiguang</module>
		<module>joolun-common-job</module>
		<module>joolun-common-nacos</module>
	</modules>

	<dependencies>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
	</dependencies>
</project>
