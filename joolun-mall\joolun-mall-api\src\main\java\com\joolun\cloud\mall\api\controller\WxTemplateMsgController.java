/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.util.ApiUtil;
import com.joolun.cloud.mall.common.feign.FeignWxTemplateMsgService;
import com.joolun.cloud.weixin.common.entity.WxTemplateMsg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 微信模板/订阅消息
 *
 * <AUTHOR>
 * @date 2020-04-16 17:30:03
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wxtemplatemsg")
@Api(value = "wxtemplatemsg", tags = "微信模板/订阅消息管理API")
public class WxTemplateMsgController {

    private final FeignWxTemplateMsgService feignWxTemplateMsgService;

    /**
     * list列表，可指定useType
     * @param wxTemplateMsg 微信模板/订阅消息
     * @return
     */
    @ApiOperation(value = "list列表")
    @PostMapping("/list")
    public R getList(HttpServletRequest request, @RequestBody WxTemplateMsg wxTemplateMsg) {
		String wxAppId = ApiUtil.getAppId(request);
		wxTemplateMsg.setAppId(wxAppId);
        return feignWxTemplateMsgService.getTemplateList(wxTemplateMsg, SecurityConstants.FROM_IN);
    }

}
