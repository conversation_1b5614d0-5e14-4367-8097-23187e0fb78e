/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.UserFootprint;
import com.joolun.cloud.mall.admin.service.UserFootprintService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户足迹
 *
 * <AUTHOR>
 * @date 2020-12-24 22:15:45
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/userfootprint")
@Api(value = "userfootprint", tags = "用户足迹管理")
public class UserFootprintController {

    private final UserFootprintService userFootprintService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param userFootprint 用户足迹
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:userfootprint:index')")
    public R getPage(Page page, UserFootprint userFootprint) {
        return R.ok(userFootprintService.page1(page, userFootprint));
    }

    /**
     * 用户足迹查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "用户足迹查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:userfootprint:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(userFootprintService.getById(id));
    }

    /**
     * 用户足迹新增
     * @param userFootprint 用户足迹
     * @return R
     */
    @ApiOperation(value = "用户足迹新增")
    @SysLog("新增用户足迹")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:userfootprint:add')")
    public R save(@RequestBody UserFootprint userFootprint) {
        return R.ok(userFootprintService.save(userFootprint));
    }

    /**
     * 用户足迹修改
     * @param userFootprint 用户足迹
     * @return R
     */
    @ApiOperation(value = "用户足迹修改")
    @SysLog("修改用户足迹")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:userfootprint:edit')")
    public R updateById(@RequestBody UserFootprint userFootprint) {
        return R.ok(userFootprintService.updateById(userFootprint));
    }

    /**
     * 用户足迹删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "用户足迹删除")
    @SysLog("删除用户足迹")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:userfootprint:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(userFootprintService.removeById(id));
    }

}
