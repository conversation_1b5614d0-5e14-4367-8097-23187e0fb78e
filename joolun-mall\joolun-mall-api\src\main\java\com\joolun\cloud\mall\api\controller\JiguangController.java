package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.jiguang.util.JiguangUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018/11/14
 * <p>
 * 极光控制类
 */
@RestController
@AllArgsConstructor
@RequestMapping("/jiguang")
@Api(value = "jiguang", tags = "极光API")
public class JiguangController {
	private final JiguangUtils jiguangUtils;
	/**
	 * 获取初始化参数
	 * @return
	 */
	@ApiOperation(value = "获取初始化参数")
	@GetMapping("/config")
	public R getConfig() {
		return R.ok(jiguangUtils.getConfig());
	}

	/**
	 * 获取消息
	 * @return
	 */
	@ApiOperation(value = "获取HTTP 验证")
	@GetMapping("/messages")
	public R getAuthString(String userName, String beginTime, String endTime) {
		return R.ok(jiguangUtils.getMessages(userName, beginTime, endTime));
	}
}
