/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.service.UserWithdrawRecordService;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.entity.UserWithdrawRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 用户提现记录
 *
 * <AUTHOR>
 * @date 2021-05-10 14:20:02
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/userwithdrawrecord")
@Api(value = "userwithdrawrecord", tags = "用户提现记录API")
public class UserWithdrawRecordController {

    private final UserWithdrawRecordService userWithdrawRecordService;

    /**
     * 用户提现记录分页列表
     * @param page 分页对象
     * @param userWithdrawRecord 用户提现记录
     * @return
     */
    @ApiOperation(value = "用户提现记录分页列表")
    @GetMapping("/page")
	@ApiLogin
    public R getPage(Page page, UserWithdrawRecord userWithdrawRecord) {
		userWithdrawRecord.setUserId(ThirdSessionHolder.getUserId());
        return R.ok(userWithdrawRecordService.page(page, Wrappers.query(userWithdrawRecord)));
    }

    /**
     * 用户提现记录新增
     * @param userWithdrawRecord 用户提现记录
     * @return R
     */
    @ApiOperation(value = "用户提现记录新增")
    @PostMapping
	@ApiLogin
    public R save(@RequestBody UserWithdrawRecord userWithdrawRecord) {
		userWithdrawRecord.setUserId(ThirdSessionHolder.getUserId());
		userWithdrawRecord.setStatus(MallConstants.USER_WITHDRAW_RECORD_STATUS_0);
        return R.ok(userWithdrawRecordService.save(userWithdrawRecord));
    }

    /**
     * 用户提现记录修改
     * @param userWithdrawRecord 用户提现记录
     * @return R
     */
	@ApiLogin
    @ApiOperation(value = "用户提现记录修改")
    @PutMapping
    public R updateById(@RequestBody UserWithdrawRecord userWithdrawRecord) {
		userWithdrawRecord.setUserId(null);
		userWithdrawRecord.setStatus(null);
        return R.ok(userWithdrawRecordService.updateById(userWithdrawRecord));
    }

    /**
     * 用户提现记录删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "用户提现记录删除")
    @DeleteMapping("/{id}")
	@ApiLogin
    public R removeById(@PathVariable String id) {
        return R.ok(userWithdrawRecordService.removeById(id));
    }

	/**
	 * 用户提现记录查询
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "用户提现记录查询")
	@GetMapping("/{id}")
	@ApiLogin
	public R getById(@PathVariable("id") String id) {
		return R.ok(userWithdrawRecordService.getById(id));
	}

}
