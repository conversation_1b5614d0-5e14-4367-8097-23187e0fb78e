/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.util.ApiUtil;
import com.joolun.cloud.mall.common.feign.FeignWxQrCodeService;
import com.joolun.cloud.weixin.common.dto.MaQrCodeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;

/**
 * 小程序码
 *
 * <AUTHOR>
 * @date 2019-10-25 15:39:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wxqrcode")
@Api(value = "wxqrcode", tags = "小程序码API")
public class WxQrCodeController {

	private final FeignWxQrCodeService feignWxQrCodeService;
	/**
	 * 生成小程序码
	 * @param request
	 * @param maQrCodeDTO
	 * @return
	 */
	@ApiOperation(value = "生成小程序码")
	@PostMapping("/unlimited")
	public R getUnlimited(HttpServletRequest request, @RequestBody MaQrCodeDTO maQrCodeDTO){
		maQrCodeDTO.setAppId(ApiUtil.getAppId(request));
		return feignWxQrCodeService.getUnlimited(maQrCodeDTO, SecurityConstants.FROM_IN);
	}
}
