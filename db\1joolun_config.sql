/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80025
 Source Host           : localhost:3306
 Source Schema         : joolun_config

 Target Server Type    : MySQL
 Target Server Version : 80025
 File Encoding         : 65001

 Date: 02/01/2022 23:52:14
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for config_info
-- ----------------------------
DROP TABLE IF EXISTS `config_info`;
CREATE TABLE `config_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `c_use` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `effect` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `c_schema` text CHARACTER SET utf8 COLLATE utf8_bin,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfo_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=731 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='config_info';

-- ----------------------------
-- Records of config_info
-- ----------------------------
BEGIN;
INSERT INTO `config_info` VALUES (1, 'dynamic_routes', 'DEFAULT_GROUP', 'routes:\n# joolun-auth\n- id: joolun-auth\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /auth/**\n  filters:\n  - name: ValidateCodeGatewayFilter\n    args: {}\n  - name: PasswordDecoderFilter\n    args: {}\n  uri: lb://joolun-auth\n  order: 0\n# joolun-upms-admin\n- id: joolun-upms-admin\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /upms/**\n  filters: \n  - name: RequestRateLimiter\n    args: \n      # 限流策略\n      key-resolver: \'#{@remoteAddrKeyResolver}\'\n      # 令牌桶每秒填充率\n      redis-rate-limiter.burstCapacity: 20\n      # 令牌桶容量\n      redis-rate-limiter.replenishRate: 20\n  uri: lb://joolun-upms-admin\n  order: 0\n# joolun-codegen\n- id: joolun-codegen\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /gen/**\n  filters: []\n  uri: lb://joolun-codegen\n  order: 0\n# joolun-weixin-admin\n- id: joolun-weixin-admin\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /weixin/**\n  filters: []\n  uri: lb://joolun-weixin-admin\n  order: 0\n# joolun-weixin-api\n- id: joolun-weixin-api\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /weixinapi/**\n  filters: []\n  uri: lb://joolun-weixin-api\n  order: 0\n# joolun-mall-admin\n- id: joolun-mall-admin\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /mall/**\n  filters: []\n  uri: lb://joolun-mall-admin\n  order: 0\n# joolun-mall-api\n- id: joolun-mall-api\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /mallapi/**\n  filters: []\n  uri: lb://joolun-mall-api\n  order: 0\n# joolun-pay-api\n- id: joolun-pay-api\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /payapi/**\n  filters: []\n  uri: lb://joolun-pay-api\n  order: 0\n# joolun-pay-apix\n- id: joolun-pay-apix\n  predicates:\n  - name: Path\n    args: \n      _genkey_0: /payapi/**\n  filters: []\n  uri: lb://joolun-pay-apix\n  order: 0', '12024b7c7db2cd75704dd34a89cf40cb', '2019-07-30 14:26:08', '2021-09-10 08:28:59', NULL, '0:0:0:0:0:0:0:1', '', '', '动态路由配置', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (2, 'application-dev.yml', 'DEFAULT_GROUP', '# 加解密根密码\njasypt:\n  encryptor:\n    #根密码，改完密码要把joolun_upms.sys_datasource数据库表清空，否则代码生成器无法启动\n    password: joolun\nspring:\n  servlet:\n    multipart:\n      location: /home\n  # redis 相关\n  redis:\n    host: joolun-redis\n    port: 6379\n    password: gongredis\n    database: 2\n# logging日志\nlogging:\n  level:\n    com.alibaba.nacos.client.naming: error\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n# feign 配置\nfeign:\n  sentinel:\n    enabled: true\n  okhttp:\n    enabled: true\n  httpclient:\n    enabled: false\n  client:\n    config:\n      default:\n        connectTimeout: 10000\n        readTimeout: 10000\n  compression:\n    request:\n      enabled: true\n    response:\n      enabled: true\n#请求处理的超时时间\nribbon:\n  ReadTimeout: 10000\n  ConnectTimeout: 10000\n# mybaits-plus配置\nmybatis-plus:\n  # MyBatis Mapper所对应的XML文件位置\n  mapper-locations: classpath:/mapper/*Mapper.xml\n  # 自定义TypeHandler\n  type-handlers-package: com.joolun.cloud.common.core.mybatis.typehandler\n  global-config:\n    sql-parser-cache: true\n    # 关闭MP3.0自带的banner\n    banner: false\n    db-config:\n      # 主键类型\n      id-type: auto\n#swagger公共信息\nswagger:\n  title: JooLun API\n  description: JooLun\n  license: Powered By joolun\n  licenseUrl: http://www.joolun.com/\n  terms-of-service-url: http://www.joolun.com/\n  authorization:\n    name: OAuth\n    auth-regex: ^.*$\n    authorization-scope-list:\n      - scope: server\n        description: server all\n    token-url-list:\n      - http://joolun-gateway:9999/auth/oauth/token\n## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      # 无需token访问的url,如果子模块重写这里的配置就会被覆盖\n      release-urls:\n        - /actuator/**\n        - /v2/api-docs\n    resource:\n      loadBalanced: true\n      token-info-uri: http://joolun-auth/oauth/check_token\n#第三方登录配置\nthirdparty:\n  #微信登录\n  wx: \n    appId: wx5251cca540acece5\n    appSecret: c45d8b740babcde766ba1eebcb617b68\n  #QQ登录\n  qq:\n    appId: 101888362\n    appKey: 17494f217014cc949a8f5a52c4c6aff2\n     \n## 文件存放目录配置（用来存放微信支付证书）\nhome-dir:\n  windows: C:/joolun-file/\n  linux: /mnt/install/joolun-file/\n\nbase:\n  #商城相关配置\n  mall:\n    #支付、物流回调地址，即网关joolun-gateway服务的外网地址，要保证外网能访问\n    notifyHost: http://test.joolun.com\n    #快递100授权key\n    logisticsKey: xxxxxxxxxxxxxx\n    #用户默认头像\n    userDefaultAvatar: http://minio.joolun.com/joolun/1/material/32f19366-3c43-4002-9a82-c984a2d20bbf.png\n\n#阿里短信配置\nsms:\n  regionId: cn-hangzhou\n  accessKeyId: LTAI5tDiziejq7QAqsgWDfYj\n  accessKeySecret: ******************************\n  #模板\n  templates:\n    #登录模板\n    signName1: gong\n    templateCode1: SMS_221120592\n\n#极光配置\njiguang:\n  appKey: d635884b4525bbe0390228b7\n  secret: 554de322c8cd3f8cdadba6f7\n  flag: 1', '731c5cd8f37991a606c9594065f932ca', '2019-07-28 23:14:26', '2021-07-29 10:36:03', NULL, '0:0:0:0:0:0:0:1', '', '', '主配置文件', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (3, 'joolun-gateway-dev.yml', 'DEFAULT_GROUP', 'security:\n  encode:\n    # 前端密码密钥，必须16位，和joolun-plus-ui、joolun-plus-app配置文件\\src\\config\\env.js中的securityKey相对应\n    key: \'3D4239E21C0513B0\'\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n# - swagger\n# - api\n  swagger-providers:\n    - joolun-auth', '0c645c7a912b700de891992ee710f086', '2019-07-28 23:14:26', '2021-12-10 06:47:46', '', '0:0:0:0:0:0:0:1', '', '', '网关配置', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (4, 'joolun-auth-dev.yml', 'DEFAULT_GROUP', '# 数据源\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: *************************************************************************************************************************************************************************************************************************************************************  freemarker:\n    allow-request-override: false\n    allow-session-override: false\n    cache: true\n    charset: UTF-8\n    check-template-location: true\n    content-type: text/html\n    enabled: true\n    expose-request-attributes: false\n    expose-session-attributes: false\n    expose-spring-macro-helpers: true\n    prefer-file-system-access: true\n    suffix: .ftl\n    template-loader-path: classpath:/templates/', '4f5675346313e7cd173860b04d84f8f7', '2019-07-28 23:14:26', '2021-06-12 10:42:18', NULL, '0:0:0:0:0:0:0:1', '', '', '认证授权配置', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (5, 'joolun-upms-admin-dev.yml', 'DEFAULT_GROUP', '## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      client-id: admin\n      client-secret: admin\n      scope: server\n      # 无需token访问的url\n      release-urls:\n        - /actuator/**\n        - /v2/api-docs\n        - /user/register\n        - /druid/**\n        - /user/count\n        - /tenant/outside/**\n# 数据源\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: *************************************************************************************************************************************************************************************************************************************************************      web-stat-filter: \n        enabled: true\n      stat-view-servlet:\n        enabled: true\n        # 设置白名单，不填则允许所有访问\n        allow:\n        deny:\n        url-pattern: /druid/*\n        # 控制台管理用户名和密码\n        login-username: admin\n        login-password: 123456\n      filter:\n        stat:\n            enabled: true\n            # 慢SQL记录\n            log-slow-sql: true\n            slow-sql-millis: 1000\n            merge-sql: true\n        wall:\n            config:\n                multi-statement-allow: true\n# Logger Config sql日志\nlogging:\n  level:\n    com.joolun.cloud.upms.admin.mapper: debug\nbase:\n  # 租户表维护\n  tenant:\n    column: tenant_id\n    tables:\n      - sys_user\n      - sys_role\n      - sys_organ\n      - sys_log\n      - sys_log_login\n      - sys_config_storage\n      - sys_config_message\n      - sys_config_editor\n      - sys_organ_relation\n      - sys_role_menu\n      - sys_user_role\n  #店铺数据权限控制\n  shop:\n    datascope:\n      mappers:\n        - column: shop_id\n          value: ShopUserMapper\n  #数据权限配置\n  datascope:\n    column: organ_id\n    mapperIds:\n      - com.joolun.cloud.upms.admin.mapper.SysUserMapper.getUserVosPage \n#邮箱配置\nemail:\n  mailSmtpHost: smtpdm.aliyun.com\n  mailSmtpUsername: xxxxxxxxx\n  mailSmtpPassword: xxxxxxxxxxx\n  siteName: JooLun', 'f9b895b2cfb42a4fc584a1f82a1803f3', '2019-07-28 23:14:26', '2021-08-16 15:15:00', NULL, '0:0:0:0:0:0:0:1', '', '', '用户权限管理配置', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (6, 'joolun-codegen-dev.yml', 'DEFAULT_GROUP', '## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      client-id: gen\n      client-secret: gen\n      scope: server\n# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: *************************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\n# Logger config sql日志\nlogging:\n  level:\n    com.joolun.cloud.codegen.mapper: debug\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n      - sys_datasource', '11b63c012489f90cbcbdcbd14543c056', '2019-07-28 23:14:26', '2021-06-21 19:46:48', NULL, '0:0:0:0:0:0:0:1', '', '', '代码生成配置', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (7, 'joolun-weixin-admin-dev.yml', 'DEFAULT_GROUP', 'security:\n  oauth2:\n    client:\n      client-id: weixin\n      client-secret: weixin\n      scope: server\n      release-urls:\n      - /actuator/**\n      - /v2/api-docs\n      - /portal/**\n      - /ws/**\n      - /open/notify/**\n      - /open/auth/**\n      - /api/**\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: ***********************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\nlogging:\n  level:\n    com.joolun.cloud.weixin.admin.mapper: debug\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n    - wx_app\n    - wx_menu\n    - wx_menu_rule\n    - wx_user\n    - wx_auto_reply\n    - wx_msg\n    - wx_mass_msg\n    - wx_template_msg\n    - wxma_code_audit\n    - wx_fast_reply\n    - wx_tag\n    - wx_tag_type\n    - wx_user_tag_link\n    - wx_page_devise\n    - wx_goods\n    - wx_goods_tag\n    - wx_goods_tag_type\n    - wx_goods_tag_link\n    - wx_mp_qr_code\n    - wx_mp_qr_code_message\n    - wx_spell_group\n    - wx_pay_shop\n    - internal_data\n    - wx_groupon_user\n    - wx_groupon_info\n    - img_share_user\n    - img_share_bind_page\n    - img_share_rules\n    - wx_user_back_list\n    - wx_goods_sort\n    - wx_goods_sort_detail\n    \n  datascope:\n    column: organ_id\n    mapperIds:\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectPage\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectList\nwx:\n  component:\n    appId: \'111111\'\n    appSecret: xxxxxxxxxxxxxxxxxxxxx\n    aesKey: xxxxxxxxxxxxxxxxxxxxx\n    token: xxxxxxxxxxxxxxxxxxxxx\n', '32b5a479d2e7bdd4b6a7f9bfb13e0445', '2019-07-28 23:14:26', '2021-12-21 09:47:07', 'nacos', '127.0.0.1', '', '', '', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (8, 'joolun-mall-admin-dev.yml', 'DEFAULT_GROUP', '## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      client-id: weixin\n      client-secret: weixin\n      scope: server\n      # 无需token访问的url\n      release-urls:\n        - /actuator/**\n        - /v2/api-docs\n        - /api/**\n        - /shopapply/addedit\n        - /shopinfo/count\n        - /shopapply/one\n# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: *************************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\n# Logger Config sql日志\nlogging:\n  level:\n    com.joolun.cloud.mall.admin.mapper: debug    \nbase:\n  # 租户表维护\n  tenant:\n    column: tenant_id\n    tables:\n      - goods_category\n      - goods_spu\n      - goods_spu_spec\n      - goods_sku\n      - goods_sku_spec_value\n      - goods_spec\n      - goods_spec_value\n      - goods_appraises\n      - shopping_cart\n      - order_info\n      - order_item\n      - order_logistics\n      - order_logistics_detail\n      - user_address\n      - user_collect\n      - material\n      - material_group\n      - order_refunds\n      - user_info\n      - points_record\n      - points_config\n      - coupon_info\n      - coupon_goods\n      - coupon_user\n      - freight_templat\n      - bargain_info\n      - bargain_user\n      - bargain_cut\n      - ensure\n      - ensure_goods\n      - groupon_info\n      - groupon_user\n      - shop_info\n      - theme_mobile\n      - seckill_hall\n      - seckill_hall_info\n      - seckill_info\n      - goods_category_shop\n      - page_devise\n      - shop_apply\n      - article_category\n      - article_info\n      - user_footprint\n      - sign_config\n      - sign_record\n      - user_shop\n      - distribution_config\n      - user_record\n      - distribution_user\n      - distribution_order\n      - distribution_order_item\n      - user_withdraw_record\n  #店铺数据权限控制\n  shop:\n    datascope:\n      mappers:\n        - column: id\n          value: ShopInfoMapper\n        - column: shop_id\n          value: GoodsSpuMapper\n        - column: shop_id\n          value: OrderInfoMapper\n        - column: shop_id\n          value: GoodsAppraisesMapper\n        - column: shop_id\n          value: OrderRefundsMapper\n        - column: shop_id\n          value: CouponInfoMapper\n        - column: shop_id\n          value: CouponUserMapper\n        - column: shop_id\n          value: FreightTemplatMapper\n        - column: shop_id\n          value: BargainInfoMapper\n        - column: shop_id\n          value: BargainUserMapper\n        - column: shop_id\n          value: BargainCutMapper\n        - column: shop_id\n          value: GrouponInfoMapper\n        - column: shop_id\n          value: GrouponUserMapper\n        - column: shop_id\n          value: PointsRecordMapper\n        - column: shop_id\n          value: MaterialGroupMapper\n        - column: shop_id\n          value: MaterialMapper\n        - column: shop_id\n          value: SeckillInfoMapper\n        - column: shop_id\n          value: GoodsCategoryShopMapper\n        - column: shop_id\n          value: PageDeviseMapper\n        - column: shop_id\n          value: UserShopMapper', 'af7a274186f2c43e39b8b14adae6d7cf', '2019-08-12 12:03:16', '2021-06-12 10:41:40', NULL, '0:0:0:0:0:0:0:1', '', '', '商城管理配置', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (9, 'joolun-mall-api-dev.yml', 'DEFAULT_GROUP', '## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      client-id: api\n      client-secret: api\n      scope: server\n      # 无需token访问的url\n      release-urls:\n        - /**\n# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: *************************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\n# Logger Config sql日志\nlogging:\n  level:\n    com.joolun.cloud.mall.api.mapper: debug    \n# 租户表维护\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n      - goods_category\n      - goods_spu\n      - goods_spu_spec\n      - goods_sku\n      - goods_sku_spec_value\n      - goods_spec\n      - goods_spec_value\n      - goods_appraises\n      - shopping_cart\n      - order_info\n      - order_item\n      - order_logistics\n      - order_logistics_detail\n      - user_address\n      - user_collect\n      - material\n      - material_group\n      - order_refunds\n      - user_info\n      - points_record\n      - points_config\n      - coupon_info\n      - coupon_goods\n      - coupon_user\n      - freight_templat\n      - bargain_info\n      - bargain_user\n      - bargain_cut\n      - ensure\n      - ensure_goods\n      - groupon_info\n      - groupon_user\n      - shop_info\n      - theme_mobile\n      - seckill_hall\n      - seckill_hall_info\n      - seckill_info\n      - goods_category_shop\n      - page_devise\n      - article_category\n      - article_info\n      - user_footprint\n      - sign_config\n      - sign_record\n      - user_shop\n      - distribution_config\n      - user_record\n      - distribution_user\n      - distribution_order\n      - distribution_order_item\n      - user_withdraw_record\nxxl:\n  job:\n    # 开关\n    enabled: false\n    admin:\n      # xxl_job后台访问地址\n      addresses: http://127.0.0.1:8080/xxl-job-admin\n    executor:\n      appname: joolun-mall-api', '21fe83ad36248618a3ebbb8c182e3d45', '2020-05-03 20:14:42', '2021-06-12 10:42:04', NULL, '0:0:0:0:0:0:0:1', '', '', '商城api模块', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (10, 'joolun-pay-api-dev.yml', 'DEFAULT_GROUP', '## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      client-id: admin\n      client-secret: admin\n      scope: server\n      # 无需token访问的url\n      release-urls:\n        - /actuator/**\n        - /v2/api-docs\n# 数据源配置\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: *************************************************************************************************************************************************************************************************************************************************************# Logger Config sql日志\nlogging:\n  level:\n    com.joolun.cloud.pay.api.mapper: debug    \n# 租户表维护\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n      - pay_config\n      - pay_apply_form\n  #店铺数据权限控制\n  shop:\n    datascope:\n      mappers:\n        - column: shop_id\n          value: PayApplyFormMapper\n\n  #固定租户支付配置\n  fixed:\n      tenantId: 1', '351499ae280e95823b6f94d8781c01f1', '2020-05-28 17:06:27', '2021-09-26 05:55:31', NULL, '0:0:0:0:0:0:0:1', '', '', '支付模块', 'null', 'null', 'yaml', 'null');
INSERT INTO `config_info` VALUES (11, 'joolun-monitor-dev.yml', 'DEFAULT_GROUP', 'spring:\n  # 安全配置\n  security:\n    user:\n      name: joolun\n      password: joolun\n  boot:\n    admin:\n      ui:\n        title: \'JooLun服务状态监控\'', '49cac710202cc7d9763270aa78f5d746', '2021-01-17 19:33:20', '2021-03-11 18:18:10', NULL, '127.0.0.1', '', '', '监控中心', '', '', 'yaml', '');
INSERT INTO `config_info` VALUES (20, 'sentinal-joolun', 'SENTINEL_GROUP', '[\n    {\n        \"resource\": \"joolun-auth\",\n        \"count\": 500,\n        \"grade\": 1,\n        \"limitApp\": \"default\",\n        \"strategy\": 0,\n        \"controlBehavior\": 0\n    },{\n        \"resource\": \"joolun-upms-admin\",\n        \"count\": 1000,\n        \"grade\": 1,\n        \"limitApp\": \"default\",\n        \"strategy\": 0,\n        \"controlBehavior\": 0\n    },{\n        \"resource\": \"joolun-weixin-admin\",\n        \"count\": 1000,\n        \"grade\": 1,\n        \"limitApp\": \"default\",\n        \"strategy\": 0,\n        \"controlBehavior\": 0\n    },{\n        \"resource\": \"joolun-mall-admin\",\n        \"count\": 1000,\n        \"grade\": 1,\n        \"limitApp\": \"default\",\n        \"strategy\": 0,\n        \"controlBehavior\": 0\n    },{\n        \"resource\": \"joolun-mall-api\",\n        \"count\": 1000,\n        \"grade\": 1,\n        \"limitApp\": \"default\",\n        \"strategy\": 0,\n        \"controlBehavior\": 0\n    },{\n        \"resource\": \"joolun-pay-api\",\n        \"count\": 1000,\n        \"grade\": 1,\n        \"limitApp\": \"default\",\n        \"strategy\": 0,\n        \"controlBehavior\": 0\n    }\n]', '3201726bc9f4ab1c2c3e46ea62edb6d9', '2021-01-17 17:38:30', '2021-01-17 17:43:30', NULL, '127.0.0.1', '', '', 'sentinal流控规则', '', '', 'json', '');
INSERT INTO `config_info` VALUES (682, 'joolun-weixin-api', 'DEFAULT_GROUP', '## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      client-id: weixin\n      client-secret: weixin\n      scope: server\n      # 无需token访问的url\n      release-urls:\n        - /**\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: ***********************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\n# Logger Config sql日志\nlogging:\n  level:\n    com.joolun.cloud.weixin.api.mapper: debug    \n# 租户表维护\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n      - wx_app\n      - wx_menu\n      - wx_menu_rule\n      - wx_user\n      - wx_auto_reply\n      - wx_msg\n      - wx_mass_msg\n      - wx_template_msg\n      - wxma_code_audit\n      - wx_fast_reply\n      - wx_tag\n      - wx_tag_type\n      - wx_user_tag_link\n      - wx_page_devise\n      - wx_goods\n      - wx_goods_tag_type\n      - order_info\n      - wx_groupon_info\n      - wx_groupon_user\n      - internal_data\n      - vote_record', '6b8b27f2d01e8254dd88ff703cd713f9', '2021-07-10 04:42:24', '2021-12-17 06:46:24', 'nacos', '127.0.0.1', '', '', '', '', '', 'yaml', '');
COMMIT;

-- ----------------------------
-- Table structure for config_info_aggr
-- ----------------------------
DROP TABLE IF EXISTS `config_info_aggr`;
CREATE TABLE `config_info_aggr` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfoaggr_datagrouptenantdatum` (`data_id`,`group_id`,`tenant_id`,`datum_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='增加租户字段';

-- ----------------------------
-- Records of config_info_aggr
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for config_info_beta
-- ----------------------------
DROP TABLE IF EXISTS `config_info_beta`;
CREATE TABLE `config_info_beta` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfobeta_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='config_info_beta';

-- ----------------------------
-- Records of config_info_beta
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for config_info_tag
-- ----------------------------
DROP TABLE IF EXISTS `config_info_tag`;
CREATE TABLE `config_info_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfotag_datagrouptenanttag` (`data_id`,`group_id`,`tenant_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='config_info_tag';

-- ----------------------------
-- Records of config_info_tag
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for config_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `config_tags_relation`;
CREATE TABLE `config_tags_relation` (
  `id` bigint NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nid`),
  UNIQUE KEY `uk_configtagrelation_configidtag` (`id`,`tag_name`,`tag_type`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='config_tag_relation';

-- ----------------------------
-- Records of config_tags_relation
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for group_capacity
-- ----------------------------
DROP TABLE IF EXISTS `group_capacity`;
CREATE TABLE `group_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='集群、各Group容量信息表';

-- ----------------------------
-- Records of group_capacity
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for his_config_info
-- ----------------------------
DROP TABLE IF EXISTS `his_config_info`;
CREATE TABLE `his_config_info` (
  `id` bigint unsigned NOT NULL,
  `nid` bigint unsigned NOT NULL AUTO_INCREMENT,
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `group_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `app_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `src_user` text CHARACTER SET utf8 COLLATE utf8_bin,
  `src_ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `op_type` char(10) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`nid`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_gmt_modified` (`gmt_modified`),
  KEY `idx_did` (`data_id`)
) ENGINE=InnoDB AUTO_INCREMENT=206 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='多租户改造';

-- ----------------------------
-- Records of his_config_info
-- ----------------------------
BEGIN;
INSERT INTO `his_config_info` VALUES (7, 201, 'joolun-weixin-admin-dev.yml', 'DEFAULT_GROUP', '', 'security:\n  oauth2:\n    client:\n      client-id: weixin\n      client-secret: weixin\n      scope: server\n      release-urls:\n      - /actuator/**\n      - /v2/api-docs\n      - /portal/**\n      - /ws/**\n      - /open/notify/**\n      - /open/auth/**\n      - /api/**\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: ***********************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\nlogging:\n  level:\n    com.joolun.cloud.weixin.admin.mapper: debug\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n    - wx_app\n    - wx_menu\n    - wx_menu_rule\n    - wx_user\n    - wx_auto_reply\n    - wx_msg\n    - wx_mass_msg\n    - wx_template_msg\n    - wxma_code_audit\n    - wx_fast_reply\n    - wx_tag\n    - wx_tag_type\n    - wx_user_tag_link\n    - wx_page_devise\n    - wx_goods\n    - wx_goods_tag\n    - wx_goods_tag_type\n    - wx_goods_tag_link\n    - wx_mp_qr_code\n    - wx_mp_qr_code_message\n    - wx_spell_group\n    - wx_pay_shop\n    - internal_data\n    - wx_groupon_user\n    - img_share_user\n    - img_share_bind_page\n    - img_share_rules\n    - wx_user_back_list\n  datascope:\n    column: organ_id\n    mapperIds:\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectPage\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectList\nwx:\n  component:\n    appId: \'111111\'\n    appSecret: xxxxxxxxxxxxxxxxxxxxx\n    aesKey: xxxxxxxxxxxxxxxxxxxxx\n    token: xxxxxxxxxxxxxxxxxxxxx\n', '0aec4fcc8efd29c8d14639995f5bf8ae', '2021-12-09 12:50:01', '2021-12-09 04:50:01', '', '127.0.0.1', 'U', '');
INSERT INTO `his_config_info` VALUES (3, 202, 'joolun-gateway-dev.yml', 'DEFAULT_GROUP', '', 'security:\n  encode:\n    # 前端密码密钥，必须16位，和joolun-plus-ui、joolun-plus-app配置文件\\src\\config\\env.js中的securityKey相对应\n    key: \'1234567891234567\'\n# 不校验验证码终端\nignore:\n  clients:\n    - test\n# - swagger\n# - api\n  swagger-providers:\n    - joolun-auth', '5a15133e841591ecd69b36ab45820a6e', '2021-12-10 14:47:47', '2021-12-10 06:47:46', '', '0:0:0:0:0:0:0:1', 'U', '');
INSERT INTO `his_config_info` VALUES (682, 203, 'joolun-weixin-api', 'DEFAULT_GROUP', '', '## spring security 配置\nsecurity:\n  oauth2:\n    client:\n      client-id: weixin\n      client-secret: weixin\n      scope: server\n      # 无需token访问的url\n      release-urls:\n        - /**\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: ***********************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\n# Logger Config sql日志\nlogging:\n  level:\n    com.joolun.cloud.weixin.api.mapper: debug    \n# 租户表维护\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n      - wx_app\n      - wx_menu\n      - wx_menu_rule\n      - wx_user\n      - wx_auto_reply\n      - wx_msg\n      - wx_mass_msg\n      - wx_template_msg\n      - wxma_code_audit\n      - wx_fast_reply\n      - wx_tag\n      - wx_tag_type\n      - wx_user_tag_link\n      - wx_page_devise\n      - wx_goods\n      - order_info\n      - wx_groupon_info\n      - wx_groupon_user\n      - internal_data\n      - vote_record', '7871c6ed20e80903394a20611c1a6196', '2021-12-17 14:46:23', '2021-12-17 06:46:24', 'nacos', '127.0.0.1', 'U', '');
INSERT INTO `his_config_info` VALUES (7, 204, 'joolun-weixin-admin-dev.yml', 'DEFAULT_GROUP', '', 'security:\n  oauth2:\n    client:\n      client-id: weixin\n      client-secret: weixin\n      scope: server\n      release-urls:\n      - /actuator/**\n      - /v2/api-docs\n      - /portal/**\n      - /ws/**\n      - /open/notify/**\n      - /open/auth/**\n      - /api/**\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: ***********************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\nlogging:\n  level:\n    com.joolun.cloud.weixin.admin.mapper: debug\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n    - wx_app\n    - wx_menu\n    - wx_menu_rule\n    - wx_user\n    - wx_auto_reply\n    - wx_msg\n    - wx_mass_msg\n    - wx_template_msg\n    - wxma_code_audit\n    - wx_fast_reply\n    - wx_tag\n    - wx_tag_type\n    - wx_user_tag_link\n    - wx_page_devise\n    - wx_goods\n    - wx_goods_tag\n    - wx_goods_tag_type\n    - wx_goods_tag_link\n    - wx_mp_qr_code\n    - wx_mp_qr_code_message\n    - wx_spell_group\n    - wx_pay_shop\n    - internal_data\n    - wx_groupon_user\n    - wx_groupon_info\n    - img_share_user\n    - img_share_bind_page\n    - img_share_rules\n    - wx_user_back_list\n  datascope:\n    column: organ_id\n    mapperIds:\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectPage\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectList\nwx:\n  component:\n    appId: \'111111\'\n    appSecret: xxxxxxxxxxxxxxxxxxxxx\n    aesKey: xxxxxxxxxxxxxxxxxxxxx\n    token: xxxxxxxxxxxxxxxxxxxxx\n', '16a27b4d26442626626d33b6f1ed8bee', '2021-12-21 16:14:50', '2021-12-21 08:14:50', 'nacos', '0:0:0:0:0:0:0:1', 'U', '');
INSERT INTO `his_config_info` VALUES (7, 205, 'joolun-weixin-admin-dev.yml', 'DEFAULT_GROUP', '', 'security:\n  oauth2:\n    client:\n      client-id: weixin\n      client-secret: weixin\n      scope: server\n      release-urls:\n      - /actuator/**\n      - /v2/api-docs\n      - /portal/**\n      - /ws/**\n      - /open/notify/**\n      - /open/auth/**\n      - /api/**\nspring:\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    druid:\n      driver-class-name: com.mysql.cj.jdbc.Driver\n      username: root\n      password: GONG.mysql8@\n      url: ***********************************************************************************************************************************************************************************************************************************************************  resources:\n    static-locations: classpath:/static/,classpath:/views/\nlogging:\n  level:\n    com.joolun.cloud.weixin.admin.mapper: debug\nbase:\n  tenant:\n    column: tenant_id\n    tables:\n    - wx_app\n    - wx_menu\n    - wx_menu_rule\n    - wx_user\n    - wx_auto_reply\n    - wx_msg\n    - wx_mass_msg\n    - wx_template_msg\n    - wxma_code_audit\n    - wx_fast_reply\n    - wx_tag\n    - wx_tag_type\n    - wx_user_tag_link\n    - wx_page_devise\n    - wx_goods\n    - wx_goods_tag\n    - wx_goods_tag_type\n    - wx_goods_tag_link\n    - wx_mp_qr_code\n    - wx_mp_qr_code_message\n    - wx_spell_group\n    - wx_pay_shop\n    - internal_data\n    - wx_groupon_user\n    - wx_groupon_info\n    - img_share_user\n    - img_share_bind_page\n    - img_share_rules\n    - wx_user_back_list\n    - wx_goods_sort\n  datascope:\n    column: organ_id\n    mapperIds:\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectPage\n    - com.joolun.cloud.weixin.admin.mapper.WxAppMapper.selectList\nwx:\n  component:\n    appId: \'111111\'\n    appSecret: xxxxxxxxxxxxxxxxxxxxx\n    aesKey: xxxxxxxxxxxxxxxxxxxxx\n    token: xxxxxxxxxxxxxxxxxxxxx\n', '67f39669f3ea3c7275040ce6afb4c6e2', '2021-12-21 17:47:07', '2021-12-21 09:47:07', 'nacos', '127.0.0.1', 'U', '');
COMMIT;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `role` varchar(50) NOT NULL,
  `resource` varchar(512) NOT NULL,
  `action` varchar(8) NOT NULL,
  UNIQUE KEY `uk_role_permission` (`role`,`resource`,`action`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of permissions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `username` varchar(50) NOT NULL,
  `role` varchar(50) NOT NULL,
  UNIQUE KEY `idx_user_role` (`username`,`role`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of roles
-- ----------------------------
BEGIN;
INSERT INTO `roles` VALUES ('nacos', 'ROLE_ADMIN');
COMMIT;

-- ----------------------------
-- Table structure for tenant_capacity
-- ----------------------------
DROP TABLE IF EXISTS `tenant_capacity`;
CREATE TABLE `tenant_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='租户容量信息表';

-- ----------------------------
-- Records of tenant_capacity
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_info_kptenantid` (`kp`,`tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin COMMENT='tenant_info';

-- ----------------------------
-- Records of tenant_info
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `branch_id` bigint NOT NULL,
  `xid` varchar(100) NOT NULL,
  `context` varchar(128) NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int NOT NULL,
  `log_created` datetime NOT NULL,
  `log_modified` datetime NOT NULL,
  `ext` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Records of undo_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `username` varchar(50) NOT NULL,
  `password` varchar(500) NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  PRIMARY KEY (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of users
-- ----------------------------
BEGIN;
INSERT INTO `users` VALUES ('nacos', '$2a$10$EuWPZHzz32dJN7jexM34MOeYirDdFAZm2kuWj7VEOJhhZkDrxfvUu', 1);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
