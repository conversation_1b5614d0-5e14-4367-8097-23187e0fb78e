<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.UserCollectMapper">

	<resultMap id="userCollectMap" type="com.joolun.cloud.mall.common.entity.UserCollect">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="type" column="type"/>
		<result property="userId" column="user_id"/>
		<result property="relationId" column="relation_id"/>
	</resultMap>

	<resultMap id="userCollectMap1" extends="userCollectMap" type="com.joolun.cloud.mall.common.entity.UserCollect">
		<collection property="userInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=user_id}">
		</collection>
		<collection property="goodsSpu" ofType="com.joolun.cloud.mall.common.entity.GoodsSpu"
					select="com.joolun.cloud.mall.admin.mapper.GoodsSpuMapper.selectById"
					column="{id=relation_id}">
		</collection>
		<collection property="shopInfo" ofType="com.joolun.cloud.mall.common.entity.ShopInfo"
					select="com.joolun.cloud.mall.admin.mapper.ShopInfoMapper.selectById"
					column="{id=relation_id}">
		</collection>
	</resultMap>

	<sql id="userCollectSql">
		user_collect.`id`,
		user_collect.`tenant_id`,
		user_collect.`del_flag`,
		user_collect.`create_time`,
		user_collect.`update_time`,
		user_collect.`type`,
		user_collect.`user_id`,
		user_collect.`relation_id`
	</sql>

	<select id="selectPage1" resultMap="userCollectMap1">
		SELECT
		<include refid="userCollectSql"/>
		FROM user_collect user_collect
		<where>
			<if test="query.type != null">
				AND user_collect.`type` = #{query.type}
			</if>
			<if test="query.userId != null">
				AND user_collect.`user_id` = #{query.userId}
			</if>
			<if test="query.relationId != null">
				AND user_collect.`relation_id` = #{query.relationId}
			</if>
		</where>
	</select>
</mapper>
