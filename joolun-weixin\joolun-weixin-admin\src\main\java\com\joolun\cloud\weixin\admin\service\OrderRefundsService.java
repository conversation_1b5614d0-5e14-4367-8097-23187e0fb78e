/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.weixin.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.cloud.weixin.common.entity.OrderRefunds;

/**
 * 退款详情
 *
 * <AUTHOR>
 * @date 2019-11-14 16:35:25
 */
public interface OrderRefundsService extends IService<OrderRefunds> {

	/**
	 * 操作发起退款
	 * @param entity
	 * @return
	 */
	boolean doOrderRefunds(OrderRefunds entity);

	IPage<OrderRefunds> page1(IPage<OrderRefunds> page, OrderRefunds orderRefunds);

	/**
	 * 处理退款结果
	 * @param orderRefunds
	 */
	void doOrderRefundsResult(OrderRefunds orderRefunds);
}
