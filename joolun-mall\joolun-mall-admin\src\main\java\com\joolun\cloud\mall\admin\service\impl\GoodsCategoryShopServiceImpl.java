/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.mall.common.entity.GoodsCategoryShop;
import com.joolun.cloud.mall.admin.mapper.GoodsCategoryShopMapper;
import com.joolun.cloud.mall.admin.service.GoodsCategoryShopService;
import com.joolun.cloud.mall.common.entity.GoodsCategoryShopTree;
import com.joolun.cloud.upms.common.util.TreeUtil;
import org.springframework.stereotype.Service;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺商品分类表
 *
 * <AUTHOR>
 * @date 2020-08-26 08:58:11
 */
@Service
public class GoodsCategoryShopServiceImpl extends ServiceImpl<GoodsCategoryShopMapper, GoodsCategoryShop> implements GoodsCategoryShopService {

	@Override
	public List<GoodsCategoryShopTree> selectTree(GoodsCategoryShop goodsCategoryShop) {
		return getTree(this.list(Wrappers.lambdaQuery(goodsCategoryShop)));
	}

	/**
	 * 构建树
	 *
	 * @param entitys
	 * @return
	 */
	private List<GoodsCategoryShopTree> getTree(List<GoodsCategoryShop> entitys) {
		List<GoodsCategoryShopTree> treeList = entitys.stream()
				.filter(entity -> !entity.getId().equals(entity.getParentId()))
				.sorted(Comparator.comparingInt(GoodsCategoryShop::getSort))
				.map(entity -> {
					GoodsCategoryShopTree node = new GoodsCategoryShopTree();
					BeanUtil.copyProperties(entity,node);
					return node;
				}).collect(Collectors.toList());
		return TreeUtil.build(treeList, CommonConstants.PARENT_ID);
	}
}
