/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.common.entity.DistributionUser;
import com.joolun.cloud.mall.admin.mapper.DistributionUserMapper;
import com.joolun.cloud.mall.admin.service.DistributionUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 分销员
 *
 * <AUTHOR>
 * @date 2021-04-25 17:40:35
 */
@Service
public class DistributionUserServiceImpl extends ServiceImpl<DistributionUserMapper, DistributionUser> implements DistributionUserService {

	@Override
	public IPage<DistributionUser> page1(IPage<DistributionUser> page, DistributionUser distributionUser) {
		return baseMapper.selectPage1(page, distributionUser);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCommissionWithdrawal(String userId, BigDecimal commission) {
		baseMapper.update(new DistributionUser(), Wrappers.<DistributionUser>lambdaUpdate()
				.eq(DistributionUser::getUserId, userId)
				.setSql(" commission_withdrawal = commission_withdrawal + " + commission));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateCommissionTotal(String userId, BigDecimal commission) {
		baseMapper.update(new DistributionUser(), Wrappers.<DistributionUser>lambdaUpdate()
				.eq(DistributionUser::getUserId, userId)
				.setSql(" commission_total = commission_total + " + commission));
	}
}
