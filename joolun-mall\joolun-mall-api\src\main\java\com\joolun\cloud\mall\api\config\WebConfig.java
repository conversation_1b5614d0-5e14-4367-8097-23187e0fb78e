package com.joolun.cloud.mall.api.config;

import com.joolun.cloud.mall.api.interceptor.ThirdSessionInterceptor;
import com.joolun.cloud.mall.common.constant.MallConstants;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web配置
 */
@Configuration
@AllArgsConstructor
public class WebConfig implements WebMvcConfigurer {
	private final RedisTemplate redisTemplate;

	/**
	 * 拦截器
	 * @param registry
	 */
	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		/**
		 * 进入ThirdSession拦截器
		 */
		registry.addInterceptor(new ThirdSessionInterceptor(redisTemplate))
				//放行接口
				.excludePathPatterns("/wxuser/loginma",//小程序登录
						"/wxuser/loginmp",//公众号h5登录
						"/thememobile",//获取商城主题装修配置
						MallConstants.ORDER_NOTIFY_URL_WX,//订单支付回调
						MallConstants.ORDER_NOTIFY_URL_ALI,//订单支付回调
						"/orderinfo/notify-logisticsr",//订单物流回调
						"/orderrefunds/notify-refunds");//订单退款回调
	}
}
