package com.joolun.cloud.common.security.thirdparty;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.constant.enums.LoginTypeEnum;
import com.joolun.cloud.common.security.component.BasePreAuthenticationChecks;
import com.joolun.cloud.common.security.service.BaseUserDetailsService;
import com.joolun.cloud.common.security.thirdparty.config.ThirdPartyConfigProperties;
import com.joolun.cloud.common.security.util.ThridPartyHttpsUtils;
import com.joolun.cloud.upms.common.entity.SysUser;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsChecker;

/**
 * <AUTHOR> 第三方登录逻辑
 */
@Slf4j
public class ThirdPartyAuthenticationProvider implements AuthenticationProvider {
	private MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();
	private UserDetailsChecker detailsChecker = new BasePreAuthenticationChecks();

	@Getter
	@Setter
	private BaseUserDetailsService userDetailsService;

	@Getter
	@Setter
	private ThirdPartyConfigProperties thirdPartyConfigProperties;

	@Override
	@SneakyThrows
	public Authentication authenticate(Authentication authentication) {
		ThirdPartyAuthenticationToken thirdPartyAuthenticationToken = (ThirdPartyAuthenticationToken) authentication;

		String principal = thirdPartyAuthenticationToken.getPrincipal().toString();
		String[] inStrs = principal.split(StringPool.AT);
		String type = inStrs[0];
		String code = inStrs[1];
		String redirectUri = inStrs[2];
		String openId = null;
		if (LoginTypeEnum.WX.getType().equals(type)) {//微信登录
			openId = ThridPartyHttpsUtils.getWxOpenId(thirdPartyConfigProperties.getWx().getAppId(), thirdPartyConfigProperties.getWx().getAppSecret(), code);
		}
		if(LoginTypeEnum.QQ.getType().equals(type)){
			openId = ThridPartyHttpsUtils.getQqOpenId(thirdPartyConfigProperties.getQq().getAppId(), thirdPartyConfigProperties.getQq().getAppKey(), code, redirectUri);
		}
		UserDetails userDetails = userDetailsService.loadUserByThirdParty(type, openId);
		if (userDetails == null) {
			log.debug("Authentication failed: no credentials provided");

			throw new BadCredentialsException(messages.getMessage(
					"AbstractUserDetailsAuthenticationProvider.noopBindAccount",
					"Noop Bind Account"));
		}

		// 检查账号状态
		detailsChecker.check(userDetails);

		ThirdPartyAuthenticationToken authenticationToken = new ThirdPartyAuthenticationToken(userDetails, userDetails.getAuthorities());
		authenticationToken.setDetails(thirdPartyAuthenticationToken.getDetails());
		return authenticationToken;
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return ThirdPartyAuthenticationToken.class.isAssignableFrom(authentication);
	}
}
