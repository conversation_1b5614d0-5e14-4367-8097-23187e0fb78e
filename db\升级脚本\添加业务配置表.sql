-- 业务配置表
CREATE TABLE `business_config` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `create_id` varchar(32) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(32) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '逻辑删除标记（0：显示；1：隐藏）',
  `tenant_id` varchar(32) NOT NULL COMMENT '所属租户',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) DEFAULT NULL COMMENT '配置值',
  `config_label` varchar(100) DEFAULT NULL COMMENT '配置标签',
  `config_type` varchar(20) DEFAULT 'text' COMMENT '配置类型（text、radio、select等）',
  `config_options` text COMMENT '配置选项（JSON格式）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_key` (`tenant_id`, `config_key`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务配置表';

-- 插入初始数据
INSERT INTO `business_config` (`id`, `tenant_id`, `config_key`, `config_value`, `config_label`, `config_type`, `config_options`, `sort_order`, `remark`, `create_time`) VALUES
('1', '1', 'timeoutRefundHandle', 'contact_service', '超时档期退款处理设置', 'radio', '[{"label":"联系客服","value":"contact_service"},{"label":"强制删除","value":"force_delete"}]', 1, '超时档期退款处理方式配置', NOW());
