/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.common.nacos.util;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import java.util.Properties;

/**
 * nacos配置文件操作工具类
 */
@Slf4j
@Configuration
@AllArgsConstructor
public class NacosConfigUtils {

	private NacosConfigProperties nacosProperties;

	/**
	 * 获取ConfigService
	 * @return
	 * @throws NacosException
	 */
	public ConfigService getConfigService() throws NacosException {
		Properties properties = new Properties();
		properties.put(PropertyKeyConst.SERVER_ADDR, nacosProperties.getServerAddr());
		properties.put(PropertyKeyConst.USERNAME, nacosProperties.getUsername());
		properties.put(PropertyKeyConst.PASSWORD, nacosProperties.getPassword());
		ConfigService configService = NacosFactory.createConfigService(properties);
		return configService;
	}
}
