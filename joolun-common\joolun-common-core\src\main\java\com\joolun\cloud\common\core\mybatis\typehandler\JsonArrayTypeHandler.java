package com.joolun.cloud.common.core.mybatis.typehandler;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;

/**
 *  存储到数据库, 将JSON对象转换成字符串;
 *  从数据库获取数据, 将字符串转为JSON对象.
 */
@MappedTypes({JSONArray.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class JsonArrayTypeHandler extends BaseTypeHandler<JSONArray> {

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, JSONArray parameter,
									JdbcType jdbcType) throws SQLException {
		ps.setString(i, JSONUtil.toJsonStr(parameter));
	}

	@Override
	public JSONArray getNullableResult(ResultSet rs, String columnName)
			throws SQLException {
		return JSONUtil.parseArray(rs.getString(columnName));
	}

	@Override
	public JSONArray getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
		return JSONUtil.parseArray(rs.getString(columnIndex));
	}

	@Override
	public JSONArray getNullableResult(CallableStatement cs, int columnIndex)
			throws SQLException {
		return JSONUtil.parseArray(cs.getString(columnIndex));
	}

}
