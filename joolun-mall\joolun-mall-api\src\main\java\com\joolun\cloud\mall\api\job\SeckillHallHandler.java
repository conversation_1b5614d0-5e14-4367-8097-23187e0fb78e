/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */

package com.joolun.cloud.mall.api.job;

import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.mall.api.service.SeckillHallService;
import com.joolun.cloud.mall.common.entity.SeckillHall;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

/**
 * 秒杀会场相关定时任务
 * <AUTHOR>
 * @date 2020-09-18
 */
@Slf4j
@Component
@AllArgsConstructor
public class SeckillHallHandler {

	private final SeckillHallService seckillHallService;

	/**
     * 定时更新指定会场的会场日期
	 * @param
     * @return
     */
	@XxlJob("seckillHallHallDateJobHandler")
	public ReturnT<String> seckillHallHallDateJobHandler(String s) {
		TenantContextHolder.setTenantId("1");
		String[] seckillHallIds = new String[]{"1306587287680811010","1306587471210971137","1320001594057596929","1320001646574477314"
				,"1320333602495438849","1320333675316944897","1320333958520545282","1320333997926031361"};
		SeckillHall seckillHall;
		LocalDateTime time=LocalDateTime.now();
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		String strDate = dtf.format(time);
		for(int i = 0; i<seckillHallIds.length; i++){
			seckillHall = new SeckillHall();
			seckillHall.setId(seckillHallIds[i]);
			seckillHall.setHallDate(strDate);
			seckillHallService.updateById(seckillHall);
		}
		TenantContextHolder.clear();
		return SUCCESS;
	}

}