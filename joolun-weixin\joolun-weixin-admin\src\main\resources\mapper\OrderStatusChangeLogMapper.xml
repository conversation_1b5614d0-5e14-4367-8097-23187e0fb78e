<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.weixin.admin.mapper.OrderStatusChangeLogMapper">

    <resultMap id="orderStatusChangeLogMap" type="com.joolun.cloud.weixin.common.entity.OrderStatusChangeLog">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="orderId" column="order_id"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="modifierName" column="modifier_name"/>
        <result property="oldStatus" column="old_status"/>
        <result property="newStatus" column="new_status"/>
        <result property="changeReason" column="change_reason"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="orderStatusChangeLogSql">
        order_status_change_log.`id`,
        order_status_change_log.`tenant_id`,
        order_status_change_log.`order_id`,
        order_status_change_log.`modifier_id`,
        order_status_change_log.`modifier_name`,
        order_status_change_log.`old_status`,
        order_status_change_log.`new_status`,
        order_status_change_log.`change_reason`,
        order_status_change_log.`create_time`,
        order_status_change_log.`update_time`,
        order_status_change_log.`del_flag`
    </sql>

</mapper>
