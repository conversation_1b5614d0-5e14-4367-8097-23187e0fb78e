<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.DistributionUserMapper">

    <resultMap id="distributionUserMap" type="com.joolun.cloud.mall.common.entity.DistributionUser">
        <id property="userId" column="user_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="commissionTotal" column="commission_total"/>
        <result property="commissionWithdrawal" column="commission_withdrawal"/>
    </resultMap>

	<resultMap id="distributionUserMap1" extends="distributionUserMap" type="com.joolun.cloud.mall.common.entity.DistributionUser">
		<collection property="userInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=user_id}">
		</collection>
	</resultMap>

    <sql id="distributionUserSql">
        distribution_user.`user_id`,
        distribution_user.`tenant_id`,
        distribution_user.`del_flag`,
        distribution_user.`create_time`,
        distribution_user.`update_time`,
        distribution_user.`commission_total`,
        distribution_user.`commission_withdrawal`
    </sql>

	<select id="selectPage1" resultMap="distributionUserMap1">
		SELECT
		<include refid="distributionUserSql"/>
		FROM distribution_user distribution_user
		<where>
			<if test="query.userId != null">
				AND distribution_user.`user_id` = #{query.userId}
			</if>
		</where>
	</select>
</mapper>
