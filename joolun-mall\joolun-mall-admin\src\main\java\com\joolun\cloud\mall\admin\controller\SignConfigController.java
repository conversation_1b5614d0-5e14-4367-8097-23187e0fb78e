/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.SignConfig;
import com.joolun.cloud.mall.admin.service.SignConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 签到设置
 *
 * <AUTHOR>
 * @date 2021-01-13 14:30:42
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/signconfig")
@Api(value = "signconfig", tags = "签到设置管理")
public class SignConfigController {

    private final SignConfigService signConfigService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param signConfig 签到设置
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:signconfig:index')")
    public R getPage(Page page, SignConfig signConfig) {
        return R.ok(signConfigService.page(page, Wrappers.query(signConfig)));
    }

    /**
     * 签到设置查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "签到设置查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:signconfig:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(signConfigService.getById(id));
    }

    /**
     * 签到设置新增
     * @param signConfig 签到设置
     * @return R
     */
    @ApiOperation(value = "签到设置新增")
    @SysLog("新增签到设置")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:signconfig:add')")
    public R save(@RequestBody SignConfig signConfig) {
		try{
			return R.ok(signConfigService.save(signConfig));
		}catch (DuplicateKeyException e){
			if(e.getMessage().contains("uk_sort")){
				return R.failed("排序重复");
			}else{
				return R.failed(e.getMessage());
			}
		}

    }

    /**
     * 签到设置修改
     * @param signConfig 签到设置
     * @return R
     */
    @ApiOperation(value = "签到设置修改")
    @SysLog("修改签到设置")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:signconfig:edit')")
    public R updateById(@RequestBody SignConfig signConfig) {
        return R.ok(signConfigService.updateById(signConfig));
    }

    /**
     * 签到设置删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "签到设置删除")
    @SysLog("删除签到设置")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:signconfig:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(signConfigService.removeById(id));
    }

}
