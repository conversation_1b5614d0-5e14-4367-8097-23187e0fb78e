/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.api.mapper.UserShopMapper;
import com.joolun.cloud.mall.api.service.UserShopService;
import com.joolun.cloud.mall.common.entity.UserShop;
import org.springframework.stereotype.Service;

/**
 * 店铺用户
 *
 * <AUTHOR>
 * @date 2021-03-29 14:26:20
 */
@Service
public class UserShopServiceImpl extends ServiceImpl<UserShopMapper, UserShop> implements UserShopService {

}
