package com.joolun.cloud.weixin.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量修改订单状态DTO
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@ApiModel(value = "批量修改订单状态DTO")
public class BatchUpdateOrderStatusDTO {

    /**
     * 订单ID列表
     */
    @ApiModelProperty(value = "订单ID列表", required = true)
    @NotEmpty(message = "订单ID列表不能为空")
    private List<String> orderIds;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态", required = true)
    @NotNull(message = "订单状态不能为空")
    private String orderStatus;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    private String changeReason;
}
