<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.BargainUserMapper">

	<resultMap id="bargainUserMap" type="com.joolun.cloud.mall.common.entity.BargainUser">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="shopId" column="shop_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="createId" column="create_id"/>
		<result property="bargainId" column="bargain_id"/>
		<result property="userId" column="user_id"/>
		<result property="spuId" column="spu_id"/>
		<result property="skuId" column="sku_id"/>
		<result property="validBeginTime" column="valid_begin_time"/>
		<result property="validEndTime" column="valid_end_time"/>
		<result property="bargainPrice" column="bargain_price"/>
		<result property="floorBuy" column="floor_buy"/>
		<result property="status" column="status"/>
		<result property="isBuy" column="is_buy"/>
		<result property="orderId" column="order_id"/>
	</resultMap>

	<resultMap id="couponUserMap1" extends="bargainUserMap" type="com.joolun.cloud.mall.common.entity.BargainUser">
		<collection property="havBargainAmount" ofType="java.math.BigDecimal"
					select="com.joolun.cloud.mall.admin.mapper.BargainCutMapper.getTotalCutPrice"
					column="{bargainUserId=id}">
		</collection>
		<collection property="userInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=user_id}">
		</collection>
		<collection property="bargainInfo" ofType="com.joolun.cloud.mall.common.entity.BargainInfo"
					select="com.joolun.cloud.mall.admin.mapper.BargainInfoMapper.selectById2"
					column="{id=bargain_id}">
		</collection>
	</resultMap>

	<resultMap id="couponUserMap2" extends="bargainUserMap" type="com.joolun.cloud.mall.common.entity.BargainUser">
		<collection property="bargainInfo" ofType="com.joolun.cloud.mall.common.entity.BargainInfo"
					select="com.joolun.cloud.mall.admin.mapper.BargainInfoMapper.selectById2"
					column="{id=bargain_id}">
		</collection>
	</resultMap>

	<sql id="bargainUserSql">
		bargain_user.`id`,
		bargain_user.`tenant_id`,
		bargain_user.`shop_id`,
		bargain_user.`del_flag`,
		bargain_user.`create_time`,
		bargain_user.`update_time`,
		bargain_user.`create_id`,
		bargain_user.`bargain_id`,
		bargain_user.`user_id`,
		bargain_user.`spu_id`,
		bargain_user.`sku_id`,
		bargain_user.`valid_begin_time`,
		bargain_user.`valid_end_time`,
		bargain_user.`bargain_price`,
		bargain_user.`floor_buy`,
		bargain_user.`status`,
		bargain_user.`is_buy`,
		bargain_user.`order_id`
	</sql>

	<select id="selectPage1" resultMap="couponUserMap1">
		SELECT
		<include refid="bargainUserSql"/>
		FROM bargain_user bargain_user
		<where>
			<if test="query.shopId != null">
				AND bargain_user.`shop_id` = #{query.shopId}
			</if>
			<if test="query.userId != null">
				AND bargain_user.`user_id` = #{query.userId}
			</if>
			<if test="query.bargainId != null">
				AND bargain_user.`bargain_id` = #{query.bargainId}
			</if>
			<if test="query.spuId != null">
				AND bargain_user.`spu_id` = #{query.spuId}
			</if>
			<if test="query.skuId != null">
				AND bargain_user.`sku_id` = #{query.skuId}
			</if>
		</where>
	</select>

</mapper>
