/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.GoodsSpuSpecValuePic;
import com.joolun.cloud.mall.admin.service.GoodsSpuSpecValuePicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * SPU规格值图片
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsspuspecvaluepic")
@Api(value = "goodsspuspecvaluepic", tags = "SPU规格值图片管理")
public class GoodsSpuSpecValuePicController {

    private final GoodsSpuSpecValuePicService goodsSpuSpecValuePicService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param goodsSpuSpecValuePic SPU规格值图片
     * @return
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:index')")
    public R getGoodsSpuSpecValuePicPage(Page page, GoodsSpuSpecValuePic goodsSpuSpecValuePic) {
        return R.ok(goodsSpuSpecValuePicService.page(page, Wrappers.query(goodsSpuSpecValuePic)));
    }

    /**
     * 通过SPU ID查询规格值图片列表
     * @param spuId SPU ID
     * @return R
     */
    @ApiOperation(value = "通过SPU ID查询规格值图片列表")
    @GetMapping("/spu/{spuId}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:get')")
    public R getListBySpuId(@PathVariable("spuId") String spuId) {
        return R.ok(goodsSpuSpecValuePicService.list(Wrappers.<GoodsSpuSpecValuePic>lambdaQuery()
                .eq(GoodsSpuSpecValuePic::getSpuId, spuId)));
    }

    /**
     * 通过id查询规格值图片
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过id查询规格值图片")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(goodsSpuSpecValuePicService.getById(id));
    }

    /**
     * 新增规格值图片
     * @param goodsSpuSpecValuePic 规格值图片
     * @return R
     */
    @ApiOperation(value = "新增规格值图片")
    @SysLog("新增规格值图片")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:add')")
    public R save(@RequestBody GoodsSpuSpecValuePic goodsSpuSpecValuePic) {
        return R.ok(goodsSpuSpecValuePicService.save(goodsSpuSpecValuePic));
    }

    /**
     * 修改规格值图片
     * @param goodsSpuSpecValuePic 规格值图片
     * @return R
     */
    @ApiOperation(value = "修改规格值图片")
    @SysLog("修改规格值图片")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:edit')")
    public R updateById(@RequestBody GoodsSpuSpecValuePic goodsSpuSpecValuePic) {
        return R.ok(goodsSpuSpecValuePicService.updateById(goodsSpuSpecValuePic));
    }

    /**
     * 通过id删除规格值图片
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过id删除规格值图片")
    @SysLog("删除规格值图片")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(goodsSpuSpecValuePicService.removeById(id));
    }

    /**
     * 批量保存规格值图片
     * @param list 规格值图片列表
     * @return R
     */
    @ApiOperation(value = "批量保存规格值图片")
    @SysLog("批量保存规格值图片")
    @PostMapping("/batch")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:add')")
    public R saveBatch(@RequestBody List<GoodsSpuSpecValuePic> list) {
        return R.ok(goodsSpuSpecValuePicService.saveBatch(list));
    }
} 