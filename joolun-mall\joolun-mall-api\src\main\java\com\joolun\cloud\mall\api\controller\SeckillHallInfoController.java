/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.common.entity.SeckillHallInfo;
import com.joolun.cloud.mall.api.service.SeckillHallInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 秒杀会场商品
 *
 * <AUTHOR>
 * @date 2020-08-12 16:18:39
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/seckillhallinfo")
@Api(value = "seckillhallinfo", tags = "秒杀会场商品API")
public class SeckillHallInfoController {

    private final SeckillHallInfoService seckillHallInfoService;

}
