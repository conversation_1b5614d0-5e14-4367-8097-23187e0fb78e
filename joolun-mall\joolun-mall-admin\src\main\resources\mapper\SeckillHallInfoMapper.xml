<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.SeckillHallInfoMapper">

    <resultMap id="seckillHallInfoMap" type="com.joolun.cloud.mall.common.entity.SeckillHallInfo">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createId" column="create_id"/>
        <result property="sort" column="sort"/>
        <result property="seckillHallId" column="seckill_hall_id"/>
        <result property="seckillInfoId" column="seckill_info_id"/>
    </resultMap>

    <sql id="seckillHallInfoSql">
        seckill_hall_info.`id`,        seckill_hall_info.`tenant_id`,        seckill_hall_info.`shop_id`,        seckill_hall_info.`del_flag`,        seckill_hall_info.`create_time`,        seckill_hall_info.`update_time`,        seckill_hall_info.`create_id`,        seckill_hall_info.`sort`,        seckill_hall_info.`seckill_hall_id`,        seckill_hall_info.`seckill_info_id`    </sql>
</mapper>
