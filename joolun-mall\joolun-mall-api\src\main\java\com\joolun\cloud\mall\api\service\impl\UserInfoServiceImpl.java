/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.IpUtils;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.core.util.SensitiveUtils;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.mall.api.service.DistributionConfigService;
import com.joolun.cloud.mall.api.service.PointsConfigService;
import com.joolun.cloud.mall.api.service.PointsRecordService;
import com.joolun.cloud.mall.api.util.ApiUtil;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import com.joolun.cloud.mall.common.config.MallConfigProperties;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.dto.UserInfoLoginDTO;
import com.joolun.cloud.mall.common.entity.PointsConfig;
import com.joolun.cloud.mall.common.entity.PointsRecord;
import com.joolun.cloud.mall.common.entity.UserInfo;
import com.joolun.cloud.mall.api.mapper.UserInfoMapper;
import com.joolun.cloud.mall.api.service.UserInfoService;
import com.joolun.cloud.mall.common.feign.FeignWxAppService;
import com.joolun.cloud.mall.common.feign.FeignWxUserService;
import com.joolun.cloud.weixin.common.constant.ConfigConstant;
import com.joolun.cloud.weixin.common.dto.LoginDTO;
import com.joolun.cloud.weixin.common.dto.WxOpenDataDTO;
import com.joolun.cloud.weixin.common.entity.ThirdSession;
import com.joolun.cloud.weixin.common.entity.WxApp;
import com.joolun.cloud.weixin.common.entity.WxUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 商城用户
 *
 * <AUTHOR>
 * @date 2019-12-04 11:09:55
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

	private final FeignWxUserService feignWxUserService;
	private final PointsConfigService pointsConfigService;
	private final PointsRecordService pointsRecordService;
	private final RedisTemplate redisTemplate;
	private final FeignWxAppService feignWxAppService;
	private final MallConfigProperties mallConfigProperties;
	private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
	private final DistributionConfigService distributionConfigService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo loginMa(LoginDTO loginDTO) {
		//查出wxapp信息
		R<WxApp> r = feignWxAppService.getById(loginDTO.getAppId(), SecurityConstants.FROM_IN);
		if(!r.isOk()){
			throw new RuntimeException(r.getMsg());
		}
		WxApp wxApp = r.getData();
		if(wxApp == null){
			throw new RuntimeException("无此小程序：" + loginDTO.getAppId() + "；请在后台添加");
		}
		TenantContextHolder.setTenantId(wxApp.getTenantId());
		//通过code获取微信用户信息
		R<WxUser> r1 = feignWxUserService.code2WxUserMa(loginDTO, SecurityConstants.FROM_IN);
		if(!r1.isOk()){
			throw new RuntimeException(r1.getMsg());
		}
		WxUser wxUser = r1.getData();
		UserInfo userInfo;
		if(StrUtil.isBlank(wxUser.getId())) {
			//新增微信用户
			wxUser.setAppId(wxApp.getId());
			wxUser.setAppType(wxApp.getAppType());
			R<WxUser> r4 = feignWxUserService.saveOrUpdate(wxUser, SecurityConstants.FROM_IN);
			if(!r4.isOk()){
				throw new RuntimeException(r4.getMsg());
			}
			wxUser = r4.getData();
		}
		if(StrUtil.isNotBlank(wxUser.getMallUserId())){
			userInfo = baseMapper.selectById(wxUser.getMallUserId());
			if(userInfo != null){
				//分销关系上级绑定，用户类型 1、老用户
				userInfo.setSharerUserCode(loginDTO.getSharerUserCode());
				distributionConfigService.parentBind(DistributionConfigService.USER_TYPE_1,userInfo);
			}else{
				userInfo = new UserInfo();
			}
		}else{
			//同一微信开放平台账号下的不同应用 同一用户的unionId是相同的
			if(StrUtil.isNotBlank(wxUser.getUnionId())){
				//如果该小程序用户有unionId，继续查找该unionId的公众号用户是否已经绑定商城用户，如有unionId相同的公众号用户，说明是同一人；小程序端就直接绑定相同的商城用户，无需再次登录绑定
				R<WxUser> r2 = feignWxUserService.getByUnionIdInside(wxUser.getUnionId(), ConfigConstant.WX_APP_TYPE_2, SecurityConstants.FROM_IN);
				WxUser wxUser2 = r2.getData();
				if(wxUser2 != null){
					//查到unionId相同的公众号用户，判断该公众号用户是否已经绑定了商城用户
					if(StrUtil.isNotBlank(wxUser2.getMallUserId())){
						userInfo = baseMapper.selectById(wxUser2.getMallUserId());
						//将该小程序用户绑定到该商城用户
						wxUser.setMallUserId(wxUser2.getMallUserId());
						feignWxUserService.saveOrUpdate(wxUser, SecurityConstants.FROM_IN);
						//分销关系上级绑定，用户类型 1、老用户
						userInfo.setSharerUserCode(loginDTO.getSharerUserCode());
						distributionConfigService.parentBind(DistributionConfigService.USER_TYPE_1,userInfo);
					}else{
						userInfo = new UserInfo();
					}
				}else{
					userInfo = new UserInfo();
				}
			}else{
				userInfo = new UserInfo();
			}
		}
		String thirdSessionKey = "wx:" + wxUser.getId() + ":" + UUID.randomUUID().toString();
		ThirdSession thirdSession = new ThirdSession();
		thirdSession.setTenantId(wxApp.getTenantId());
		thirdSession.setAppId(wxApp.getId());
		thirdSession.setSessionKey(wxUser.getSessionKey());
		thirdSession.setWxUserId(wxUser.getId());
		thirdSession.setOpenId(wxUser.getOpenId());
		thirdSession.setUserId(userInfo.getId());
		//将3rd_session和用户信息存入redis，并设置过期时间
		String key = MallConstants.THIRD_SESSION_BEGIN + ":" + thirdSessionKey;
		redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(thirdSession) , MallConstants.TIME_OUT_SESSION, TimeUnit.HOURS);
		userInfo.setThirdSession(thirdSessionKey);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo loginMp(LoginDTO loginDTO, HttpServletRequest request) {
		//查出wxapp信息
		R<WxApp> r = feignWxAppService.getById(loginDTO.getAppId(), SecurityConstants.FROM_IN);
		if(!r.isOk()){
			throw new RuntimeException(r.getMsg());
		}
		WxApp wxApp = r.getData();
		if(wxApp == null){
			throw new RuntimeException("无此公众号："+loginDTO.getAppId()+ "；请在后台添加");
		}
		if(!request.getHeader(MallConstants.HEADER_TENANT_ID).equals(wxApp.getTenantId())){
			throw new RuntimeException("该appID不属于该租户ID");
		}
		TenantContextHolder.setTenantId(wxApp.getTenantId());
		//通过code获取微信用户信息
		R<WxUser> r1 = feignWxUserService.code2WxUserMp(loginDTO, SecurityConstants.FROM_IN);
		if(!r1.isOk()){
			throw new RuntimeException(r1.getMsg());
		}
		WxUser wxUser = r1.getData();
		UserInfo userInfo;
		if(StrUtil.isBlank(wxUser.getId())) {
			//新增微信用户
			wxUser.setAppId(wxApp.getId());
			wxUser.setAppType(wxApp.getAppType());
			wxUser.setSubscribe(ConfigConstant.SUBSCRIBE_TYPE_WEBLICENS);
			R<WxUser> r4 = feignWxUserService.saveOrUpdate(wxUser, SecurityConstants.FROM_IN);
			if(!r4.isOk()){
				throw new RuntimeException(r4.getMsg());
			}
			wxUser = r4.getData();
		}
		if(StrUtil.isNotBlank(wxUser.getMallUserId())){
			userInfo = baseMapper.selectById(wxUser.getMallUserId());
			if(userInfo != null){
				//分销关系上级绑定，用户类型 1、老用户
				userInfo.setSharerUserCode(loginDTO.getSharerUserCode());
				distributionConfigService.parentBind(DistributionConfigService.USER_TYPE_1,userInfo);
			}else{
				userInfo = new UserInfo();
			}
		}else{
			//同一微信开放平台账号下的不同应用 同一用户的unionId是相同的
			if(StrUtil.isNotBlank(wxUser.getUnionId())){
				//如果该公众号用户有unionId，继续查找该unionId的小程序用户是否已经绑定商城用户，如有unionId相同的小程序用户，说明是同一人；公众号端就直接绑定相同的商城用户，无需再次登录绑定
				R<WxUser> r2 = feignWxUserService.getByUnionIdInside(wxUser.getUnionId(), ConfigConstant.WX_APP_TYPE_1, SecurityConstants.FROM_IN);
				WxUser wxUser2 = r2.getData();
				if(wxUser2 != null){
					//查到unionId相同的小程序用户，判断该小程序用户是否已经绑定了商城用户
					if(StrUtil.isNotBlank(wxUser2.getMallUserId())){
						userInfo = baseMapper.selectById(wxUser2.getMallUserId());
						//将该小程序用户绑定到该商城用户
						wxUser.setMallUserId(wxUser2.getMallUserId());
						feignWxUserService.saveOrUpdate(wxUser, SecurityConstants.FROM_IN);
						//分销关系上级绑定，用户类型 1、老用户
						userInfo.setSharerUserCode(loginDTO.getSharerUserCode());
						distributionConfigService.parentBind(DistributionConfigService.USER_TYPE_1,userInfo);
					}else{
						userInfo = new UserInfo();
					}
				}else{
					userInfo = new UserInfo();
				}
			}else{
				userInfo = new UserInfo();
			}
		}
		String thirdSessionKey = "wx:" + wxUser.getId() + ":" + UUID.randomUUID().toString();
		ThirdSession thirdSession = new ThirdSession();
		thirdSession.setTenantId(wxApp.getTenantId());
		thirdSession.setAppId(wxApp.getId());
		thirdSession.setWxUserId(wxUser.getId());
		thirdSession.setOpenId(wxUser.getOpenId());
		thirdSession.setUserId(userInfo.getId());
		//将3rd_session和用户信息存入redis，并设置过期时间
		String key = MallConstants.THIRD_SESSION_BEGIN + ":" + thirdSessionKey;
		redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(thirdSession) , MallConstants.TIME_OUT_SESSION, TimeUnit.HOURS);
		userInfo.setThirdSession(thirdSessionKey);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo updateByMa(WxOpenDataDTO wxOpenDataDTO) {
		//修改微信用户信息
		R<WxUser> r = feignWxUserService.updateByMaInside(wxOpenDataDTO, SecurityConstants.FROM_IN);
		if(!r.isOk()){
			throw new RuntimeException(r.getMsg());
		}
		UserInfo userInfo = null;
		//修改商城用户信息
		WxUser wxUser = r.getData();
		if(wxUser != null && StrUtil.isNotBlank(wxUser.getMallUserId())){
			userInfo = baseMapper.selectById(wxUser.getMallUserId());
			userInfo.setNickName(wxUser.getNickName());
			userInfo.setHeadimgUrl(wxUser.getHeadimgUrl());
//			userInfo.setSex(wxUser.getSex());
//			userInfo.setCity(wxUser.getCity());
//			userInfo.setCountry(wxUser.getCountry());
//			userInfo.setProvince(wxUser.getProvince());
			userInfo.setUpdateTime(LocalDateTime.now());
			if(MallConstants.USER_GRADE_0.equals(userInfo.getUserGrade())){
				userInfo.setUserGrade(MallConstants.USER_GRADE_1);//1：普通会员
				//获取会员初始积分
				PointsConfig pointsConfig = pointsConfigService.getOne(Wrappers.emptyWrapper());
				int initVipPosts = pointsConfig != null ? pointsConfig.getInitVipPosts() : 0;
				userInfo.setPointsCurrent(userInfo.getPointsCurrent() + initVipPosts);
				if(initVipPosts > 0){
					//新增积分变动记录
					PointsRecord pointsRecord = new PointsRecord();
					pointsRecord.setUserId(userInfo.getId());
					pointsRecord.setAmount(initVipPosts);
					pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_1);
					pointsRecord.setDescription("会员初始积分");
					pointsRecordService.save(pointsRecord);
				}
			}
			baseMapper.updateById(userInfo);
		}
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo updateByMp(WxOpenDataDTO wxOpenDataDTO) {
		R<WxUser> r = feignWxUserService.updateByMpInside(wxOpenDataDTO, SecurityConstants.FROM_IN);
		if(!r.isOk()){
			throw new RuntimeException(r.getMsg());
		}
		UserInfo userInfo = null;
		//修改商城用户信息
		WxUser wxUser = r.getData();
		if(wxUser != null && StrUtil.isNotBlank(wxUser.getMallUserId())){
			userInfo = baseMapper.selectById(wxUser.getMallUserId());
			userInfo.setNickName(wxUser.getNickName());
			userInfo.setHeadimgUrl(wxUser.getHeadimgUrl());
//			userInfo.setSex(wxUser.getSex());
//			userInfo.setCity(wxUser.getCity());
//			userInfo.setCountry(wxUser.getCountry());
//			userInfo.setProvince(wxUser.getProvince());
			userInfo.setUpdateTime(LocalDateTime.now());
			if(MallConstants.USER_GRADE_0.equals(userInfo.getUserGrade())){
				userInfo.setUserGrade(MallConstants.USER_GRADE_1);//1：普通会员
				//获取会员初始积分
				PointsConfig pointsConfig = pointsConfigService.getOne(Wrappers.emptyWrapper());
				int initVipPosts = pointsConfig != null ? pointsConfig.getInitVipPosts() : 0;
				userInfo.setPointsCurrent(userInfo.getPointsCurrent() + initVipPosts);
				if(initVipPosts > 0){
					//新增积分变动记录
					PointsRecord pointsRecord = new PointsRecord();
					pointsRecord.setUserId(userInfo.getId());
					pointsRecord.setAmount(initVipPosts);
					pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_1);
					pointsRecord.setDescription("会员初始积分");
					pointsRecordService.save(pointsRecord);
				}
			}
			baseMapper.updateById(userInfo);
		}
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo saveUserInfo(UserInfo userInfo) {
		if(StrUtil.isBlank(userInfo.getId())){
			//获取用户初始积分
			PointsConfig pointsConfig = pointsConfigService.getOne(Wrappers.emptyWrapper());
			int initPosts = pointsConfig != null ? pointsConfig.getInitPosts() : 0;
			//新增商城用户
			userInfo.setUserGrade(MallConstants.USER_GRADE_0);
			userInfo.setPointsCurrent(initPosts);
			if(StrUtil.isBlank(userInfo.getHeadimgUrl())){//没有头像则入默认头像
				userInfo.setHeadimgUrl(mallConfigProperties.getUserDefaultAvatar());
			}
			if(StrUtil.isBlank(userInfo.getNickName())){//没有昵称则入默认昵称
				String nickName = SensitiveUtils.mobilePhone(userInfo.getPhone());
				userInfo.setNickName(nickName);
			}
			baseMapper.insert(userInfo);
			if(initPosts > 0){
				//新增积分变动记录
				PointsRecord pointsRecord = new PointsRecord();
				pointsRecord.setUserId(userInfo.getId());
				pointsRecord.setAmount(initPosts);
				pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_0);
				pointsRecord.setDescription("用户初始积分");
				pointsRecordService.save(pointsRecord);
			}
		}else{
			baseMapper.updateById(userInfo);
		}
		userInfo = baseMapper.selectById(userInfo.getId());
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo loginByPhoneWx(ThirdSession thirdSession, String phone, String key, String sharerUserCode) {
		//先查询该手机号是不是商城用户
		UserInfo userInfo = baseMapper.selectOne(Wrappers.<UserInfo>lambdaQuery()
				.eq(UserInfo::getPhone,phone));
		String userType = DistributionConfigService.USER_TYPE_1;
		//先获取微信用户信息
		WxUser wxUser = feignWxUserService.getById(thirdSession.getWxUserId(), SecurityConstants.FROM_IN).getData();
		if(userInfo == null){//不是商城用户，则新增
			userInfo = new UserInfo();
			userType = DistributionConfigService.USER_TYPE_2;
			//新增商城用户
			userInfo.setAppType(MallConstants.CLIENT_TYPE_MA);
			userInfo.setAppId(wxUser.getAppId());
		}
		if (StrUtil.isBlank(userInfo.getPhone())) {
			userInfo.setPhone(phone);
		}
		if (StrUtil.isBlank(userInfo.getNickName())) {
			userInfo.setNickName(wxUser.getNickName());
		}
//		if (StrUtil.isBlank(userInfo.getCity())) {
//			userInfo.setCity(wxUser.getCity());
//		}
//		if (StrUtil.isBlank(userInfo.getCountry())) {
//			userInfo.setCountry(wxUser.getCountry());
//		}
		if (StrUtil.isBlank(userInfo.getHeadimgUrl())) {
			userInfo.setHeadimgUrl(wxUser.getHeadimgUrl());
		}
//		if (StrUtil.isBlank(userInfo.getProvince())) {
//			userInfo.setProvince(wxUser.getProvince());
//		}
//		if (StrUtil.isBlank(userInfo.getSex())) {
//			userInfo.setSex(wxUser.getSex());
//		}
		//更新或新增商城用户
		userInfo = this.saveUserInfo(userInfo);
		//分销关系上级绑定
		userInfo.setSharerUserCode(sharerUserCode);
		distributionConfigService.parentBind(userType,userInfo);

		String mallUserId = userInfo.getId();
		wxUser.setMallUserId(mallUserId);
		wxUser.setPhone(phone);

		userInfo.setThirdSession(StrUtil.removeAll(key,MallConstants.THIRD_SESSION_BEGIN + ":"));
		//更新微信用户
		feignWxUserService.saveOrUpdate(wxUser, SecurityConstants.FROM_IN);
		//更新redis中的thirdSession
		thirdSession.setUserId(mallUserId);
		redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(thirdSession) , MallConstants.TIME_OUT_SESSION, TimeUnit.HOURS);
		//处理微信用户登录、退出时的redis缓存
		this.wxUserRedisHandle(wxUser,key);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo loginByPhone(HttpServletRequest request, UserInfoLoginDTO userInfoLoginDTO) {
		//通过手机号登录
		UserInfo userInfo = this.getOne(Wrappers.<UserInfo>query().lambda()
				.eq(UserInfo::getPhone, userInfoLoginDTO.getPhone()));
		String userType = DistributionConfigService.USER_TYPE_1;
		if(userInfo == null){
			userType = DistributionConfigService.USER_TYPE_2;
			//新用户先注册
			userInfo = new UserInfo();
			userInfo.setAppType(ApiUtil.getClientType(request));
			userInfo.setAppId(ApiUtil.getAppId(request));
			userInfo.setPhone(userInfoLoginDTO.getPhone());
			//更新或新增商城用户
			userInfo = this.saveUserInfo(userInfo);
		}
		//登录
		userInfo = this.login(request,userInfo);
		//分销关系上级绑定
		userInfo.setSharerUserCode(userInfoLoginDTO.getSharerUserCode());
		distributionConfigService.parentBind(userType,userInfo);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo login(HttpServletRequest request, UserInfo userInfo) {
		String clientTypeHeader = ApiUtil.getClientType(request);
		String appIdHeader = ApiUtil.getAppId(request);
		ThirdSession thirdSession;
		String key;
		if(MallConstants.CLIENT_TYPE_H5.equals(clientTypeHeader) || MallConstants.CLIENT_TYPE_H5_PC.equals(clientTypeHeader) ||
				(MallConstants.CLIENT_TYPE_H5_WX.equals(clientTypeHeader) && StrUtil.isBlank(appIdHeader)) ||
				MallConstants.CLIENT_TYPE_APP.equals(clientTypeHeader)){//普通h5端、没有appId的微信H5、APP
			String thirdSessionKey = UUID.randomUUID().toString();
			thirdSession = new ThirdSession();
			thirdSession.setTenantId(userInfo.getTenantId());
			thirdSession.setUserId(userInfo.getId());
			//将3rd_session和用户信息存入redis，并设置过期时间
			key = MallConstants.THIRD_SESSION_BEGIN + ":" + thirdSessionKey;

			userInfo.setThirdSession(thirdSessionKey);
		}else{//小程序、公众号端，更新thirdSession即可
			thirdSession = ThirdSessionHolder.getThirdSession();
			String thirdSessionHeader = request.getHeader(MallConstants.HEADER_THIRDSESSION);
			key = MallConstants.THIRD_SESSION_BEGIN + ":" + thirdSessionHeader;
			thirdSession.setUserId(userInfo.getId());

			userInfo.setThirdSession(thirdSessionHeader);
			//先获取微信用户信息
			WxUser wxUser = feignWxUserService.getById(thirdSession.getWxUserId(), SecurityConstants.FROM_IN).getData();
			wxUser.setMallUserId(userInfo.getId());
			//更新微信用户
			feignWxUserService.saveOrUpdate(wxUser, SecurityConstants.FROM_IN);
			//处理微信用户登录、退出时的redis缓存
			this.wxUserRedisHandle(wxUser,key);
		}
		redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(thirdSession) , MallConstants.TIME_OUT_SESSION, TimeUnit.HOURS);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo logout(HttpServletRequest request) {
		//获取thirdSession
		ThirdSession thirdSession = ThirdSessionHolder.getThirdSession();
		if(thirdSession == null){
			return null;
		}
		String clientTypeHeader = ApiUtil.getClientType(request);
		String appIdHeader = ApiUtil.getAppId(request);
		String thirdSessionHeader = request.getHeader(MallConstants.HEADER_THIRDSESSION);
		String key = MallConstants.THIRD_SESSION_BEGIN + ":" + thirdSessionHeader;
		//查询出来当前商城用户信息
		UserInfo userInfo = baseMapper.selectById(thirdSession.getUserId());
		if(MallConstants.CLIENT_TYPE_H5.equals(clientTypeHeader) || MallConstants.CLIENT_TYPE_H5_PC.equals(clientTypeHeader) ||
				(MallConstants.CLIENT_TYPE_H5_WX.equals(clientTypeHeader) && StrUtil.isBlank(appIdHeader))||
				MallConstants.CLIENT_TYPE_APP.equals(clientTypeHeader)){//普通h5端或没有appId的微信H5
			//删除redis中的session
			redisTemplate.delete(key);
			return null;
		}else{//微信用户
			//先获取微信用户信息
			WxUser wxUser = feignWxUserService.getById(thirdSession.getWxUserId(), SecurityConstants.FROM_IN).getData();
			wxUser.setMallUserId("");
			//移除微信用户的商城用户ID关联
			feignWxUserService.saveOrUpdate(wxUser, SecurityConstants.FROM_IN);
			//更新redis中的thirdSession
			thirdSession.setUserId(null);
			redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(thirdSession) , MallConstants.TIME_OUT_SESSION, TimeUnit.HOURS);
			//处理微信用户登录、退出时的redis缓存
			this.wxUserRedisHandle(wxUser,key);
			userInfo = new UserInfo();
			userInfo.setThirdSession(StrUtil.removeAll(key,MallConstants.THIRD_SESSION_BEGIN + ":"));
			return userInfo;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo register(UserInfo userInfo) {
		userInfo.setPassword(ENCODER.encode(userInfo.getPassword()));
		userInfo = this.saveUserInfo(userInfo);
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updatePoints(String userId, Integer posts) {
		baseMapper.update(new UserInfo(),Wrappers.<UserInfo>lambdaUpdate()
				.eq(UserInfo::getId, userId)
				.setSql(" points_current = points_current + " + posts));
	}

	@Override
	public IPage<UserInfo> page1(IPage<UserInfo> page, UserInfo userInfo) {

		return baseMapper.selectPage1(page,userInfo);
	}

	@Override
	public void updateProCity(HttpServletRequest request, String userId) {
		Map<String, String> map = IpUtils.getProCity(ServletUtil.getClientIP(request));
		if(map != null){
			UserInfo userInfo = new UserInfo();
			userInfo.setId(userId);
			String pro = map.get("pro");
			if(StrUtil.isNotEmpty(pro)){
				userInfo.setProvince(pro);
			}
			String city = map.get("city");
			if(StrUtil.isNotEmpty(city)){
				userInfo.setCity(city);
			}
			baseMapper.updateById(userInfo);
		}
	}

	/**
	 * 处理微信用户登录、退出时的redis缓存
	 * @param wxUser
	 * @param key
	 */
	public void wxUserRedisHandle(WxUser wxUser, String key){
		if(wxUser != null){
			//清空该用户其他地方登录的缓存
			String key1 = MallConstants.THIRD_SESSION_BEGIN + ":wx:" + wxUser.getId() + ":*";
			Set<String> keys1 = redisTemplate.keys(key1);
			keys1.remove(key);
			if (CollectionUtils.isNotEmpty(keys1)) {
				redisTemplate.delete(keys1);
			}

			if(StrUtil.isNotBlank(wxUser.getUnionId())){
				//如该微信用户有unionId，则清空相同unionId的其他用户缓存
				String appType = ConfigConstant.WX_APP_TYPE_1.equals(wxUser.getAppType()) ? ConfigConstant.WX_APP_TYPE_2 : ConfigConstant.WX_APP_TYPE_1;
				WxUser wxUser2 = feignWxUserService.getByUnionIdInside(wxUser.getUnionId(), appType, SecurityConstants.FROM_IN).getData();
				if(wxUser2 != null){
					String key2 = MallConstants.THIRD_SESSION_BEGIN + ":wx:" + wxUser2.getId() + ":*";
					Set<String> keys2 = redisTemplate.keys(key2);
					if (CollectionUtils.isNotEmpty(keys2)) {
						redisTemplate.delete(keys2);
					}
				}
			}
		}
	}
}
