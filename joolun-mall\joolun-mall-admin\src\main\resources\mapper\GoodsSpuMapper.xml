<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.GoodsSpuMapper">
	<resultMap id="goodsSpuMap" type="com.joolun.cloud.mall.common.entity.GoodsSpu">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="shopId" column="shop_id"/>
		<result property="spuCode" column="spu_code"/>
		<result property="name" column="name"/>
		<result property="sellPoint" column="sell_point"/>
		<result property="description" column="description"/>
		<result property="categoryFirst" column="category_first"/>
		<result property="categorySecond" column="category_second"/>
		<result property="categoryShopFirst" column="category_shop_first"/>
		<result property="categoryShopSecond" column="category_shop_second"/>
		<result property="picUrls" column="pic_urls"/>
		<result property="shelf" column="shelf"/>
		<result property="sort" column="sort"/>
		<result property="priceDown" column="price_down"/>
		<result property="priceUp" column="price_up"/>
		<result property="saleNum" column="sale_num"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="specType" column="spec_type"/>
		<result property="delFlag" column="del_flag"/>
		<result property="pointsGiveSwitch" column="points_give_switch"/>
		<result property="pointsGiveNum" column="points_give_num"/>
		<result property="pointsDeductSwitch" column="points_deduct_switch"/>
		<result property="pointsDeductScale" column="points_deduct_scale"/>
		<result property="pointsDeductAmount" column="points_deduct_amount"/>
		<result property="freightTemplatId" column="freight_templat_id"/>
		<result property="verifyStatus" column="verify_status"/>
		<result property="verifyDetail" column="verify_detail"/>
	</resultMap>

	<resultMap id="goodsSpuMap1" extends="goodsSpuMap" type="com.joolun.cloud.mall.common.entity.GoodsSpu">
		<collection property="skus" ofType="com.joolun.cloud.mall.common.entity.GoodsSku"
					select="com.joolun.cloud.mall.admin.mapper.GoodsSkuMapper.listGoodsSkuBySpuId"
					column="{spuId=id}">
		</collection>
		<collection property="freightTemplat" ofType="com.joolun.cloud.mall.common.entity.FreightTemplat"
					select="com.joolun.cloud.mall.admin.mapper.FreightTemplatMapper.selectById"
					column="{id=freight_templat_id}">
		</collection>
		<collection property="ensureIds" ofType="java.lang.String"
					select="com.joolun.cloud.mall.admin.mapper.EnsureGoodsMapper.listEnsureIdsBySpuId" column="id">
		</collection>
	</resultMap>

	<resultMap id="goodsSpuMap2" extends="goodsSpuMap" type="com.joolun.cloud.mall.common.entity.GoodsSpu">
		<collection property="skus" ofType="com.joolun.cloud.mall.common.entity.GoodsSku"
					select="com.joolun.cloud.mall.admin.mapper.GoodsSkuMapper.listGoodsSkuBySpuId"
					column="{spuId=id}">
		</collection>
		<collection property="freightTemplat" ofType="com.joolun.cloud.mall.common.entity.FreightTemplat"
					select="com.joolun.cloud.mall.admin.mapper.FreightTemplatMapper.selectById"
					column="{id=freight_templat_id}">
		</collection>
	</resultMap>

	<resultMap id="goodsSpuMap3" extends="goodsSpuMap" type="com.joolun.cloud.mall.common.entity.GoodsSpu">
		<collection property="freightTemplat" ofType="com.joolun.cloud.mall.common.entity.FreightTemplat"
					select="com.joolun.cloud.mall.admin.mapper.FreightTemplatMapper.selectById"
					column="{id=freight_templat_id}">
		</collection>
	</resultMap>

	<resultMap id="goodsSpuMap4" extends="goodsSpuMap" type="com.joolun.cloud.mall.common.entity.GoodsSpu">
		<collection property="freightTemplat" ofType="com.joolun.cloud.mall.common.entity.FreightTemplat"
					select="com.joolun.cloud.mall.admin.mapper.FreightTemplatMapper.selectById"
					column="{id=freight_templat_id}">
		</collection>
	</resultMap>

	<sql id="goodsSpuSql">
		goods_spu.`id`,
		goods_spu.`tenant_id`,
		goods_spu.`shop_id`,
		goods_spu.`spu_code`,
		goods_spu.`name`,
		goods_spu.`sell_point`,
		goods_spu.`description`,
		goods_spu.`category_first`,
		goods_spu.`category_second`,
		goods_spu.`category_shop_first`,
		goods_spu.`category_shop_second`,
		goods_spu.`pic_urls`,
		goods_spu.`shelf`,
		goods_spu.`sort`,
		goods_spu.`price_down`,
		goods_spu.`price_up`,
		goods_spu.`sale_num`,
		goods_spu.`create_time`,
		goods_spu.`update_time`,
		goods_spu.`spec_type`,
		goods_spu.`del_flag`,
		goods_spu.`points_give_switch`,
		goods_spu.`points_give_num`,
		goods_spu.`points_deduct_switch`,
		goods_spu.`points_deduct_scale`,
		goods_spu.`points_deduct_amount`,
		goods_spu.`freight_templat_id`,
		goods_spu.`verify_status`,
		goods_spu.`verify_detail`
	</sql>

	<sql id="goodsSpuSql2">
		goods_spu.`id`,
		goods_spu.`tenant_id`,
		goods_spu.`shop_id`,
		goods_spu.`spu_code`,
		goods_spu.`name`,
		goods_spu.`sell_point`,
		null `description`,
		goods_spu.`category_first`,
		goods_spu.`category_second`,
		goods_spu.`category_shop_first`,
		goods_spu.`category_shop_second`,
		goods_spu.`pic_urls`,
		goods_spu.`shelf`,
		goods_spu.`sort`,
		goods_spu.`price_down`,
		goods_spu.`price_up`,
		goods_spu.`sale_num`,
		goods_spu.`create_time`,
		goods_spu.`update_time`,
		goods_spu.`spec_type`,
		goods_spu.`del_flag`,
		goods_spu.`points_give_switch`,
		goods_spu.`points_give_num`,
		goods_spu.`points_deduct_switch`,
		goods_spu.`points_deduct_scale`,
		goods_spu.`points_deduct_amount`,
		goods_spu.`freight_templat_id`,
		goods_spu.`verify_status`,
		goods_spu.`verify_detail`
	</sql>

	<select id="selectPage1" resultMap="goodsSpuMap">
		SELECT
		<include refid="goodsSpuSql2"/>
		FROM goods_spu goods_spu
		<where>
			<if test="query.shopId != null">
				AND goods_spu.`shop_id` = #{query.shopId}
			</if>
			<if test="query.shelf != null">
				AND goods_spu.`shelf` = #{query.shelf}
			</if>
			<if test="query.verifyStatus != null and query.verifyStatus != ''">
				AND goods_spu.`verify_status` = #{query.verifyStatus}
			</if>
			<if test="query.categorySecond != null">
				AND goods_spu.`category_second` = #{query.categorySecond}
			</if>
			<if test="query.name != null">
				AND goods_spu.`name` LIKE CONCAT('%',#{query.name},'%')
			</if>
			<if test="query.spuCode != null">
				AND goods_spu.`spu_code` = #{query.spuCode}
			</if>
			<if test="query.specType != null">
				AND goods_spu.`spec_type` = #{query.specType}
			</if>
			<if test="query.pointsGiveSwitch != null">
				AND goods_spu.`points_give_switch` = #{query.pointsGiveSwitch}
			</if>
			<if test="query.pointsDeductSwitch != null">
				AND goods_spu.`points_deduct_switch` = #{query.pointsDeductSwitch}
			</if>
		</where>
	</select>

	<select id="selectById1" resultMap="goodsSpuMap1">
		SELECT
		<include refid="goodsSpuSql"/>
		FROM goods_spu goods_spu
		WHERE goods_spu.`id` = #{id}
	</select>

	<select id="selectOneByShoppingCart" resultMap="goodsSpuMap3">
		SELECT
			<include refid="goodsSpuSql2"/>
		FROM goods_spu goods_spu
		WHERE goods_spu.`id` = #{id}
	</select>

	<select id="selectListByCouponId" resultMap="goodsSpuMap">
		SELECT
			<include refid="goodsSpuSql2"/>
		FROM goods_spu goods_spu
		LEFT JOIN coupon_goods coupon_goods
		ON goods_spu.`id` = coupon_goods.`spu_id`
		WHERE coupon_goods.`coupon_id` = #{couponId}
	</select>

	<select id="selectById4" resultMap="goodsSpuMap4">
		SELECT
		<include refid="goodsSpuSql"/>
		FROM goods_spu goods_spu
		WHERE goods_spu.`id` = #{id}
	</select>
</mapper>
