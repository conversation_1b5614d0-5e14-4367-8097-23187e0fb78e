package com.joolun.cloud.weixin.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.common.security.util.SecurityUtils;
import com.joolun.cloud.weixin.admin.mapper.OrderStatusChangeLogMapper;
import com.joolun.cloud.weixin.admin.service.OrderStatusChangeLogService;
import com.joolun.cloud.weixin.common.entity.OrderStatusChangeLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 订单状态变更日志
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@Service
public class OrderStatusChangeLogServiceImpl extends ServiceImpl<OrderStatusChangeLogMapper, OrderStatusChangeLog> implements OrderStatusChangeLogService {

    @Override
    public void logStatusChange(String orderId, String oldStatus, String newStatus, String changeReason) {
        try {
            OrderStatusChangeLog changeLog = new OrderStatusChangeLog();
            changeLog.setTenantId(TenantContextHolder.getTenantId());
            changeLog.setOrderId(orderId);
            changeLog.setModifierId(SecurityUtils.getUser().getId());
            changeLog.setModifierName(SecurityUtils.getUser().getUsername());
            changeLog.setOldStatus(oldStatus);
            changeLog.setNewStatus(newStatus);
            changeLog.setChangeReason(changeReason);
            changeLog.setCreateTime(LocalDateTime.now());
            changeLog.setUpdateTime(LocalDateTime.now());
            changeLog.setDelFlag("0");

            this.save(changeLog);
            log.info("订单状态变更日志记录成功，订单ID：{}，状态变更：{} -> {}", orderId, oldStatus, newStatus);
        } catch (Exception e) {
            log.error("记录订单状态变更日志失败，订单ID：{}，错误信息：{}", orderId, e.getMessage(), e);
        }
    }
}
