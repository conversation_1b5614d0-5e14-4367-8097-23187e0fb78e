/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.admin.service.GoodsSpuSpecValuePicService;
import com.joolun.cloud.mall.common.entity.GoodsSpec;
import com.joolun.cloud.mall.common.entity.GoodsSpecValue;
import com.joolun.cloud.mall.common.entity.GoodsSpuSpec;
import com.joolun.cloud.mall.admin.service.GoodsSpuSpecService;
import com.joolun.cloud.mall.common.entity.GoodsSpuSpecValuePic;
import com.joolun.cloud.mall.common.vo.GoodsSpecLeafVO;
import com.joolun.cloud.mall.common.vo.GoodsSpecVO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * spu规格
 *
 * <AUTHOR>
 * @date 2019-08-13 16:56:46
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsspuspec")
@Api(value = "goodsspuspec", tags = "spu规格管理")
public class GoodsSpuSpecController {

    private final GoodsSpuSpecService goodsSpuSpecService;
    private final GoodsSpuSpecValuePicService goodsSpuSpecValuePicService;

    /**
     * 分页查询
     *
     * @param page         分页对象
     * @param goodsSpuSpec spu规格
     * @return
     */
    @ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:index')")
    public R getGoodsSpuSpecPage(Page page, GoodsSpuSpec goodsSpuSpec) {
        return R.ok(goodsSpuSpecService.page(page, Wrappers.query(goodsSpuSpec)));
    }

    /**
     * 获取商品规格
     *
     * @param goodsSpuSpec
     * @return
     */
    @ApiOperation(value = "获取商品规格")
    @GetMapping("/tree")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:index')")
    public R getGoodsSpuSpecTree(GoodsSpuSpec goodsSpuSpec) {
        List<GoodsSpecVO> specList = goodsSpuSpecService.tree(goodsSpuSpec);

        // 如果指定了SPU，则获取该SPU下的规格值图片
        if (goodsSpuSpec != null && goodsSpuSpec.getSpuId() != null && !goodsSpuSpec.getSpuId().isEmpty()) {
            String spuId = goodsSpuSpec.getSpuId();
            List<GoodsSpuSpecValuePic> specValuePics = goodsSpuSpecValuePicService.list(
                    Wrappers.<GoodsSpuSpecValuePic>lambdaQuery()
                            .eq(GoodsSpuSpecValuePic::getSpuId, spuId)
            );

            if (specValuePics != null && !specValuePics.isEmpty()) {
                Map<String, String> picMap = specValuePics.stream()
                        .collect(Collectors.toMap(
                                GoodsSpuSpecValuePic::getSpecValueId,
                                GoodsSpuSpecValuePic::getPic,
                                (v1, v2) -> v1));

                // 遍历规格列表，为规格值添加图片
                for (GoodsSpecVO spec : specList) {
                    if (spec.getLeaf() != null && !spec.getLeaf().isEmpty()) {
                        for (GoodsSpecLeafVO leafValue : spec.getLeaf()) {
                            if (picMap.containsKey(leafValue.getId())) {
                                // 在GoodsSpecLeafVO中添加pic字段
                                leafValue.setPic(picMap.get(leafValue.getId()));
                            }
                        }
                    }
                }
            }
        }

        return R.ok(specList);
    }

    /**
     * 通过id查询spu规格
     *
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过id查询spu规格")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(goodsSpuSpecService.getById(id));
    }

    /**
     * 新增spu规格
     *
     * @param goodsSpuSpec spu规格
     * @return R
     */
    @ApiOperation(value = "新增spu规格")
    @SysLog("新增spu规格")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:add')")
    public R save(@RequestBody GoodsSpuSpec goodsSpuSpec) {
        return R.ok(goodsSpuSpecService.save(goodsSpuSpec));
    }

    /**
     * 修改spu规格
     *
     * @param goodsSpuSpec spu规格
     * @return R
     */
    @ApiOperation(value = "修改spu规格")
    @SysLog("修改spu规格")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:edit')")
    public R updateById(@RequestBody GoodsSpuSpec goodsSpuSpec) {
        return R.ok(goodsSpuSpecService.updateById(goodsSpuSpec));
    }

    /**
     * 通过id删除spu规格
     *
     * @param id
     * @return R
     */
    @ApiOperation(value = "通过id删除spu规格")
    @SysLog("删除spu规格")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(goodsSpuSpecService.removeById(id));
    }

}
