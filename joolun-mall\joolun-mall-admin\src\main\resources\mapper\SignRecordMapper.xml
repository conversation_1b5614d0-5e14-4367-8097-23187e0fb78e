<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.SignRecordMapper">

    <resultMap id="signRecordMap" type="com.joolun.cloud.mall.common.entity.SignRecord">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="continuDays" column="continu_days"/>
        <result property="cumulateDays" column="cumulate_days"/>
    </resultMap>

	<resultMap id="signRecordMap1" extends="signRecordMap" type="com.joolun.cloud.mall.common.entity.SignRecord">
		<collection property="userInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=user_id}">
		</collection>
	</resultMap>

    <sql id="signRecordSql">
        sign_record.`id`,
        sign_record.`tenant_id`,
        sign_record.`del_flag`,
        sign_record.`create_time`,
        sign_record.`update_time`,
        sign_record.`user_id`,
        sign_record.`continu_days`,
        sign_record.`cumulate_days`
    </sql>

	<select id="selectPage1" resultMap="signRecordMap1">
		SELECT
		<include refid="signRecordSql"/>
		FROM sign_record sign_record
		<where>
			<if test="query.userId != null">
				AND sign_record.`user_id` = #{query.userId}
			</if>
		</where>
	</select>
</mapper>
