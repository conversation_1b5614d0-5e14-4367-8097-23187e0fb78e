/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.common.entity.ArticleCategory;
import com.joolun.cloud.mall.api.service.ArticleCategoryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 文章分类
 *
 * <AUTHOR>
 * @date 2020-11-24 16:25:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/articlecategory")
@Api(value = "articlecategory", tags = "文章分类API")
public class ArticleCategoryController {

    private final ArticleCategoryService articleCategoryService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param articleCategory 文章分类
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public R getPage(Page page, ArticleCategory articleCategory) {
		articleCategory.setEnable(CommonConstants.YES);
        return R.ok(articleCategoryService.page(page, Wrappers.query(articleCategory)));
    }

    /**
     * 文章分类查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "文章分类查询")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        return R.ok(articleCategoryService.getById(id));
    }

}
