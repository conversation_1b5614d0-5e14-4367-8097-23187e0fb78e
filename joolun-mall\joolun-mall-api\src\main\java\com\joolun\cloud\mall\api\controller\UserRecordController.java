/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.service.UserRecordService;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import com.joolun.cloud.mall.common.entity.UserRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 用户消费记录API
 *
 * <AUTHOR>
 * @date 2021-04-22 11:18:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/userrecord")
@Api(value = "userrecord", tags = "用户消费记录API")
public class UserRecordController {

    private final UserRecordService userRecordService;

    /**
     * 用户消费记录查询
     * @return R
     */
    @ApiOperation(value = "用户消费记录查询")
    @GetMapping
	@ApiLogin
    public R getById() {
		UserRecord userRecord = userRecordService.getById(ThirdSessionHolder.getUserId());
		if(userRecord == null){
			userRecord = new UserRecord();
			userRecord.setTotalAmount(BigDecimal.ZERO);
			userRecord.setTotalOrder(0);
		}
        return R.ok(userRecord);
    }

}
