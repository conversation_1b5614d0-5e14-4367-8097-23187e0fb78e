<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.OrderItemMapper">

	<resultMap id="orderItemMap" type="com.joolun.cloud.mall.common.entity.OrderItem">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="shopId" column="shop_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="orderId" column="order_id"/>
		<result property="spuId" column="spu_id"/>
		<result property="spuName" column="spu_name"/>
		<result property="specInfo" column="spec_info"/>
		<result property="skuId" column="sku_id"/>
		<result property="picUrl" column="pic_url"/>
		<result property="quantity" column="quantity"/>
		<result property="salesPrice" column="sales_price"/>
		<result property="freightPrice" column="freight_price"/>
		<result property="paymentPrice" column="payment_price"/>
		<result property="paymentPoints" column="payment_points"/>
		<result property="paymentCouponPrice" column="payment_coupon_price"/>
		<result property="paymentPointsPrice" column="payment_points_price"/>
		<result property="couponUserId" column="coupon_user_id"/>
		<result property="remark" column="remark"/>
		<result property="status" column="status"/>
		<result property="isRefund" column="is_refund"/>
	</resultMap>

	<resultMap id="orderItemMap2" extends="orderItemMap" type="com.joolun.cloud.mall.common.entity.OrderItem">
		<collection property="listOrderRefunds" ofType="com.joolun.cloud.mall.common.entity.OrderRefunds"
					select="com.joolun.cloud.mall.admin.mapper.OrderRefundsMapper.selectList2"
					column="{query.orderId=order_id,query.orderItemId=id}">
		</collection>
	</resultMap>

	<sql id="orderItemSql">
		order_item.`id`,
		order_item.`tenant_id`,
		order_item.`shop_id`,
		order_item.`del_flag`,
		order_item.`create_time`,
		order_item.`update_time`,
		order_item.`order_id`,
		order_item.`spu_id`,
		order_item.`spu_name`,
		order_item.`spec_info`,
		order_item.`sku_id`,
		order_item.`pic_url`,
		order_item.`quantity`,
		order_item.`sales_price`,
		order_item.`freight_price`,
		order_item.`payment_price`,
		order_item.`payment_points`,
		order_item.`payment_coupon_price`,
		order_item.`payment_points_price`,
		order_item.`coupon_user_id`,
		order_item.`remark`,
		order_item.`status`,
		order_item.`is_refund`
	</sql>

	<select id="selectList2" resultMap="orderItemMap">
		SELECT
		<include refid="orderItemSql"/>
		FROM order_item order_item
		WHERE order_item.`order_id` = #{orderId}
	</select>

	<select id="selectById2" resultMap="orderItemMap2">
		SELECT
		<include refid="orderItemSql"/>
		FROM order_item order_item
		WHERE order_item.`id` = #{id}
	</select>
</mapper>
