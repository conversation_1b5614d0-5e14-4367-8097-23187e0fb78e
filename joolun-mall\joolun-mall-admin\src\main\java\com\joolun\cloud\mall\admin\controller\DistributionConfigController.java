/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.DistributionConfig;
import com.joolun.cloud.mall.admin.service.DistributionConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 分销设置
 *
 * <AUTHOR>
 * @date 2021-04-13 15:21:00
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/distributionconfig")
@Api(value = "distributionconfig", tags = "分销设置管理")
public class DistributionConfigController {

    private final DistributionConfigService distributionConfigService;

    /**
     * 分销设置分页列表
     * @param page 分页对象
     * @param distributionConfig 分销设置
     * @return
     */
    @ApiOperation(value = "分销设置分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:distributionconfig:index')")
    public R getPage(Page page, DistributionConfig distributionConfig) {
        return R.ok(distributionConfigService.page(page, Wrappers.query(distributionConfig)));
    }

    /**
     * 分销设置查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销设置查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionconfig:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(distributionConfigService.getById(id));
    }

    /**
     * 分销设置新增
     * @param distributionConfig 分销设置
     * @return R
     */
    @ApiOperation(value = "分销设置新增")
    @SysLog("新增分销设置")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionconfig:add')")
    public R save(@RequestBody DistributionConfig distributionConfig) {
        return R.ok(distributionConfigService.save(distributionConfig));
    }

    /**
     * 分销设置修改
     * @param distributionConfig 分销设置
     * @return R
     */
    @ApiOperation(value = "分销设置修改")
    @SysLog("修改分销设置")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionconfig:edit')")
    public R updateById(@RequestBody DistributionConfig distributionConfig) {
		if(StrUtil.isNotBlank(distributionConfig.getId())){
			distributionConfigService.updateById(distributionConfig);
			return R.ok(distributionConfig);
		}else{
			distributionConfigService.save(distributionConfig);
			return R.ok(distributionConfig);
		}
    }

    /**
     * 分销设置删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销设置删除")
    @SysLog("删除分销设置")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionconfig:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(distributionConfigService.removeById(id));
    }

	/**
	 * 查询积分设置
	 * @return R
	 */
	@ApiOperation(value = "查询积分设置")
	@GetMapping()
	public R get() {
		return R.ok(distributionConfigService.getOne(Wrappers.emptyWrapper()));
	}
}
