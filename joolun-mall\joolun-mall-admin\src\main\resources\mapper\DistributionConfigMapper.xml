<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.DistributionConfigMapper">

    <resultMap id="distributionConfigMap" type="com.joolun.cloud.mall.common.entity.DistributionConfig">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="enable" column="enable"/>
        <result property="bindType" column="bind_type"/>
        <result property="distributionModel" column="distribution_model"/>
        <result property="firstRebate" column="first_rebate"/>
        <result property="secondRebate" column="second_rebate"/>
        <result property="frozenTime" column="frozen_time"/>
        <result property="withdrawMin" column="withdraw_min"/>
        <result property="withdrawBank" column="withdraw_bank"/>
        <result property="picUrls" column="pic_urls"/>
		<result property="fullAmount" column="full_amount"/>
    </resultMap>

    <sql id="distributionConfigSql">
        distribution_config.`id`,
        distribution_config.`tenant_id`,
        distribution_config.`del_flag`,
        distribution_config.`create_time`,
        distribution_config.`update_time`,
        distribution_config.`enable`,
        distribution_config.`bind_type`,
        distribution_config.`distribution_model`,
        distribution_config.`first_rebate`,
        distribution_config.`second_rebate`,
        distribution_config.`frozen_time`,
        distribution_config.`withdraw_min`,
        distribution_config.`withdraw_bank`,
        distribution_config.`pic_urls`,
        distribution_config.`full_amount`
    </sql>
</mapper>
