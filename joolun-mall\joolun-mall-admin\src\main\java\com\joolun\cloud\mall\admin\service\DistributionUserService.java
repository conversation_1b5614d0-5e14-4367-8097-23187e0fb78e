/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.cloud.mall.common.entity.DistributionUser;

import java.math.BigDecimal;

/**
 * 分销员
 *
 * <AUTHOR>
 * @date 2021-04-25 17:40:35
 */
public interface DistributionUserService extends IService<DistributionUser> {

	IPage<DistributionUser> page1(IPage<DistributionUser> page, DistributionUser distributionUser);

	/**
	 * 统一更新操作已提现佣金金额
	 * @param userId 用户ID
	 * @param commission 金额变量。+加，-减
	 */
	void updateCommissionWithdrawal(String userId, BigDecimal commission);

	/**
	 * 统一更新操作累计佣金金额
	 * @param userId 用户ID
	 * @param commission 金额变量。+加，-减
	 */
	void updateCommissionTotal(String userId, BigDecimal commission);
}
