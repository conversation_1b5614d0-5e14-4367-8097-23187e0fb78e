/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.config.MallConfigProperties;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.entity.ShopApply;
import com.joolun.cloud.mall.admin.service.ShopApplyService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 店铺入驻申请表
 *
 * <AUTHOR>
 * @date 2020-10-28 10:39:58
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/shopapply")
@Api(value = "shopapply", tags = "店铺入驻申请表管理")
public class ShopApplyController {

    private final ShopApplyService shopApplyService;
	private final MallConfigProperties mallConfigProperties;
	private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

    /**
     * 分页列表
     * @param page 分页对象
     * @param shopApply 店铺入驻申请表
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:shopapply:index')")
    public R getPage(Page page, ShopApply shopApply) {
        return R.ok(shopApplyService.page(page, Wrappers.query(shopApply)));
    }

    /**
     * 店铺入驻申请表查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺入驻申请表查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:shopapply:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(shopApplyService.getById(id));
    }

    /**
     * 店铺入驻申请表修改
     * @param shopApply 店铺入驻申请表
     * @return R
     */
    @ApiOperation(value = "店铺入驻申请表修改")
    @PutMapping("/addedit")
    public R updateById(@RequestBody ShopApply shopApply) {
		if(StrUtil.isNotBlank(shopApply.getId())){
			shopApplyService.updateById(shopApply);
		}else{
			shopApply.setStatus(MallConstants.SHOPAPPLY_STATUS_0);
			shopApply.setImgUrl(mallConfigProperties.getUserDefaultAvatar());
			shopApply.setUserAvatar(mallConfigProperties.getUserDefaultAvatar());
			TenantContextHolder.setTenantId(shopApply.getTenantId());
			shopApply.setUserPassword(shopApply.getUserPassword());
			shopApply.setTenantId(null);
			shopApplyService.save(shopApply);
			TenantContextHolder.clear();
		}
		return R.ok(shopApply);
    }

    /**
     * 店铺入驻申请表删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺入驻申请表删除")
    @SysLog("删除店铺入驻申请表")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:shopapply:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(shopApplyService.removeById(id));
    }

	/**
	 * 店铺入驻申请审核
	 * @param shopApply 店铺入驻申请表
	 * @return R
	 */
	@ApiOperation(value = "店铺入驻申请审核")
	@PutMapping("/verify")
	@PreAuthorize("@ato.hasAuthority('mall:shopapply:verify')")
	public R verify(@RequestBody ShopApply shopApply) {
		return R.ok(shopApplyService.verify(shopApply));
	}

	/**
	 * 查询
	 * @param shopApply
	 * @return
	 */
	@ApiOperation(value = "数量查询")
	@GetMapping("/one")
	public R get(ShopApply shopApply) {
		TenantContextHolder.setTenantId(shopApply.getTenantId());
		shopApply = shopApplyService.getById(shopApply.getId());
		return R.ok(shopApply);
	}
}
