<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.ShopApplyMapper">

    <resultMap id="shopApplyMap" type="com.joolun.cloud.mall.common.entity.ShopApply">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="phone" column="phone"/>
        <result property="name" column="name"/>
        <result property="imgUrl" column="img_url"/>
        <result property="city" column="city"/>
        <result property="country" column="country"/>
        <result property="province" column="province"/>
        <result property="address" column="address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="detail" column="detail"/>
        <result property="userUsername" column="user_username"/>
        <result property="userPassword" column="user_password"/>
        <result property="userPhone" column="user_phone"/>
        <result property="userEmail" column="user_email"/>
        <result property="userAvatar" column="user_avatar"/>
        <result property="userNickname" column="user_nickname"/>
        <result property="status" column="status"/>
        <result property="applyDetail" column="apply_detail"/>
        <result property="shopId" column="shop_id"/>
    </resultMap>

</mapper>
