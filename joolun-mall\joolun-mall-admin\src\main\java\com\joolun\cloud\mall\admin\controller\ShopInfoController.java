/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.common.security.annotation.Inside;
import com.joolun.cloud.mall.admin.mapper.ShopInfo2Mapper;
import com.joolun.cloud.mall.admin.mapper.UserCollectMapper;
import com.joolun.cloud.mall.common.entity.ShopInfo;
import com.joolun.cloud.mall.admin.service.ShopInfoService;
import com.joolun.cloud.mall.common.entity.UserCollect;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 店铺表
 *
 * <AUTHOR>
 * @date 2020-05-15 13:35:18
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/shopinfo")
@Api(value = "shopinfo", tags = "店铺表管理")
public class ShopInfoController {

    private final ShopInfoService shopInfoService;
	private final UserCollectMapper userCollectMapper;
	private final ShopInfo2Mapper shopInfo2Mapper;
	/**
	 * list查询
	 * @param shopInfo 商城用户
	 * @return
	 */
	@ApiOperation(value = "list查询")
	@GetMapping("/list")
	public R getUserinfoList(ShopInfo shopInfo) {
		return R.ok(shopInfoService.list(Wrappers.query(shopInfo).lambda()
				.select(ShopInfo::getId,
						ShopInfo::getName)));
	}

    /**
     * 分页列表
     * @param page 分页对象
     * @param shopInfo 店铺表
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:shopinfo:index')")
    public R getPage(Page page, ShopInfo shopInfo) {
        return R.ok(shopInfoService.page(page, Wrappers.query(shopInfo)));
    }

    /**
     * 店铺表查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺表查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:shopinfo:get')")
    public R getById(@PathVariable("id") String id) {
		ShopInfo shopInfo = shopInfoService.getById(id);
		int count = userCollectMapper.selectCount(Wrappers.<UserCollect>lambdaQuery()
				.eq(UserCollect::getRelationId,shopInfo.getId()));
		shopInfo.setCollectCount(count);
        return R.ok(shopInfo);
    }

    /**
     * 店铺表新增
     * @param shopInfo 店铺表
     * @return R
     */
    @ApiOperation(value = "店铺表新增")
    @SysLog("新增店铺表")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:shopinfo:add')")
    public R save(@RequestBody ShopInfo shopInfo) {
        return R.ok(shopInfoService.save(shopInfo));
    }

    /**
     * 店铺表修改
     * @param shopInfo 店铺表
     * @return R
     */
    @ApiOperation(value = "店铺表修改")
    @SysLog("修改店铺表")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:shopinfo:edit')")
    public R updateById(@RequestBody ShopInfo shopInfo) {
        return R.ok(shopInfoService.updateById(shopInfo));
    }

    /**
     * 店铺表删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺表删除")
    @SysLog("删除店铺表")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:shopinfo:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(shopInfoService.removeById(id));
    }

	/**
	 * 数量查询
	 * @param shopInfo 商城用户
	 * @return
	 */
	@ApiOperation(value = "数量查询")
	@GetMapping("/count")
	public R getUserinfoCount(ShopInfo shopInfo) {
		TenantContextHolder.setTenantId(shopInfo.getTenantId());
		int count = shopInfo2Mapper.selectCount(Wrappers.query(shopInfo));
		TenantContextHolder.clear();
		return R.ok(count);
	}

	/**
	 * 更新店铺AliAuthToken
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "更新店铺AliAuthToken")
	@Inside
	@PutMapping("/aliauthtoken/{id}/{aliAuthToken}")
	Boolean updateAliAuthTokenInside(@PathVariable("id") String id, @PathVariable("aliAuthToken") String aliAuthToken){
		ShopInfo shopInfo = new ShopInfo();
		shopInfo.setId(id);
		shopInfo.setAliAuthToken(aliAuthToken);
		return shopInfoService.updateById(shopInfo);
	}

	/**
	 * 更新店铺wxMchId
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "更新店铺wxMchId")
	@Inside
	@PutMapping("/wxmchid/{id}/{wxMchId}")
	Boolean updateWxMchIdInside(@PathVariable("id") String id, @PathVariable("wxMchId") String wxMchId){
		ShopInfo shopInfo = new ShopInfo();
		shopInfo.setId(id);
		shopInfo.setWxMchId(wxMchId);
		return shopInfoService.updateById(shopInfo);
	}
}
