/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.SignRecord;
import com.joolun.cloud.mall.admin.service.SignRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 签到记录
 *
 * <AUTHOR>
 * @date 2021-01-13 15:29:30
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/signrecord")
@Api(value = "signrecord", tags = "签到记录管理")
public class SignRecordController {

    private final SignRecordService signRecordService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param signRecord 签到记录
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:signrecord:index')")
    public R getPage(Page page, SignRecord signRecord) {
        return R.ok(signRecordService.page1(page, signRecord));
    }

    /**
     * 签到记录查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "签到记录查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:signrecord:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(signRecordService.getById(id));
    }

}
