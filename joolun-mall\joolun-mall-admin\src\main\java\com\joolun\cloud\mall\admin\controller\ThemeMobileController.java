/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.ThemeMobile;
import com.joolun.cloud.mall.admin.service.ThemeMobileService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 移动端主题配置
 *
 * <AUTHOR>
 * @date 2020-06-04 13:49:31
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/thememobile")
@Api(value = "thememobile", tags = "移动端主题配置管理")
public class ThemeMobileController {

	private final ThemeMobileService themeMobileService;

	/**
	 * 分页列表
	 * @param page 分页对象
	 * @param themeMobile 移动端主题配置
	 * @return
	 */
	@ApiOperation(value = "分页列表")
	@GetMapping("/page")
	@PreAuthorize("@ato.hasAuthority('mall:thememobile:index')")
	public R getPage(Page page, ThemeMobile themeMobile) {
		return R.ok(themeMobileService.page(page, Wrappers.query(themeMobile)));
	}

	/**
	 * 移动端主题配置查询
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "移动端主题配置查询")
	@GetMapping("/{id}")
	@PreAuthorize("@ato.hasAuthority('mall:thememobile:get')")
	public R getById(@PathVariable("id") String id) {
		return R.ok(themeMobileService.getById(id));
	}

	/**
	 * 移动端主题配置新增
	 * @param themeMobile 移动端主题配置
	 * @return R
	 */
	@ApiOperation(value = "移动端主题配置新增")
	@SysLog("新增移动端主题配置")
	@PostMapping
	@PreAuthorize("@ato.hasAuthority('mall:thememobile:add')")
	public R save(@RequestBody ThemeMobile themeMobile) {
		return R.ok(themeMobileService.save(themeMobile));
	}

	/**
	 * 移动端主题配置修改
	 * @param themeMobile 移动端主题配置
	 * @return R
	 */
	@ApiOperation(value = "移动端主题配置修改")
	@SysLog("修改移动端主题配置")
	@PutMapping
	@PreAuthorize("@ato.hasAuthority('mall:thememobile:edit')")
	public R updateById(@RequestBody ThemeMobile themeMobile) {
		if(StrUtil.isNotBlank(themeMobile.getId())){
			themeMobileService.updateById(themeMobile);
		}else{
			themeMobileService.save(themeMobile);
		}
		return R.ok(themeMobile);
	}

	/**
	 * 移动端主题配置删除
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "移动端主题配置删除")
	@SysLog("删除移动端主题配置")
	@DeleteMapping("/{id}")
	@PreAuthorize("@ato.hasAuthority('mall:thememobile:del')")
	public R removeById(@PathVariable String id) {
		themeMobileService.removeById(id);
		return R.ok();
	}

	/**
	 * 查询移动端主题配置
	 * @return R
	 */
	@ApiOperation(value = "查询移动端主题配置")
	@GetMapping()
	public R get() {
		return R.ok(themeMobileService.getOne(Wrappers.emptyWrapper()));
	}
}
