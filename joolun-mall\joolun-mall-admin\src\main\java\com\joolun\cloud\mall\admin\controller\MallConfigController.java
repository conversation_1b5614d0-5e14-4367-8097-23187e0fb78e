/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.core.util.SensitiveUtils;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.common.nacos.util.NacosConfigUtils;
import com.joolun.cloud.mall.common.config.MallConfigProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.yaml.snakeyaml.Yaml;

import java.util.HashMap;
import java.util.Map;

/**
 * 商城配置
 *
 * <AUTHOR>
 * @date 2020-11-24 14:44:15
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/mallconfig")
@Api(value = "mallConfig", tags = "商城配置管理")
public class MallConfigController {

	private NacosConfigUtils nacosConfigUtils;

    /**
     * 商城配置查询
     * @param
     * @return R
     */
    @ApiOperation(value = "商城配置查询")
    @GetMapping
    @PreAuthorize("@ato.hasAuthority('mall:mallconfig:get')")
    public R getBy() throws NacosException {
		ConfigService configService = nacosConfigUtils.getConfigService();
		String content = configService.getConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP, CommonConstants.CONFIG_TIMEOUT_MS);
		Yaml yaml = new Yaml();
		Map<String,Map> confMap= yaml.load(content);
		Object object = confMap.get("base").get("mall");
		JSONObject jSONObject = JSONUtil.parseObj(object);
		MallConfigProperties mallConfigProperties = jSONObject.toBean(MallConfigProperties.class);
		//数据脱敏
		String sensitiveLogisticsKey = SensitiveUtils.process(mallConfigProperties.getLogisticsKey());
		mallConfigProperties.setLogisticsKey(sensitiveLogisticsKey);
		return R.ok(mallConfigProperties);
    }

    /**
     * 商城配置修改
     * @param mallConfigProperties 商城配置
     * @return R
     */
    @ApiOperation(value = "商城配置修改")
    @SysLog("修改商城配置")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:mallconfig:edit')")
    public R updateById(@RequestBody MallConfigProperties mallConfigProperties) throws NacosException {
		ConfigService configService = nacosConfigUtils.getConfigService();
		String content = configService.getConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP, CommonConstants.CONFIG_TIMEOUT_MS);
		Yaml yaml = new Yaml();
		Map<String,Map> confMap= yaml.load(content);
		Map<String,Map> baseMap= confMap.get("base");

		String logisticsKey = baseMap.get("mall").get("logisticsKey")+"";
		String sensitiveLogisticsKey = SensitiveUtils.process(logisticsKey);
		if(!sensitiveLogisticsKey.equals(mallConfigProperties.getLogisticsKey())){
			logisticsKey = mallConfigProperties.getLogisticsKey();
		}

		Map mallMap = new HashMap();
		mallMap.put("notifyHost",mallConfigProperties.getNotifyHost());
		mallMap.put("logisticsKey",logisticsKey);
		mallMap.put("userDefaultAvatar",mallConfigProperties.getUserDefaultAvatar());
		baseMap.put("mall",mallMap);
		confMap.put("base",baseMap);
		String confStr = yaml.dumpAsMap(confMap);
		boolean rs = configService.publishConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP,confStr,"yaml");
        return R.ok(rs);
    }

}
