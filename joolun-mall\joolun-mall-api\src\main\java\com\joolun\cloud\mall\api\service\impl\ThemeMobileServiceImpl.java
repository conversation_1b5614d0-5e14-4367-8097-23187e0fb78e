/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CacheConstants;
import com.joolun.cloud.mall.api.mapper.ThemeMobileMapper;
import com.joolun.cloud.mall.api.service.ThemeMobileService;
import com.joolun.cloud.mall.common.entity.ThemeMobile;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * 移动端主题配置
 *
 * <AUTHOR>
 * @date 2020-06-04 14:28:15
 */
@Service
public class ThemeMobileServiceImpl extends ServiceImpl<ThemeMobileMapper, ThemeMobile> implements ThemeMobileService {

	@Override
	@Cacheable(value = CacheConstants.MALL_THEME_MOBILE_CACHE, key = "#tenantId", unless = "#result == null")
	public ThemeMobile getByTenantId(Serializable tenantId) {
		return baseMapper.selectOne(Wrappers.emptyWrapper());
	}

}
