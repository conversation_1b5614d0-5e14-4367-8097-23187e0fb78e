/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */

package com.joolun.cloud.mall.api.job;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.mall.api.service.OrderInfoService;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.entity.OrderInfo;
import com.joolun.cloud.upms.common.entity.SysTenant;
import com.joolun.cloud.upms.common.feign.FeignTenantService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.List;

import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

/**
 * 订单相关定时任务
 * <AUTHOR>
 * @date 2020-09-18
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderJobHandler {

	private final OrderInfoService orderInfoService;
	private final FeignTenantService feignTenantService;
	/**
	 * 定时清理未正常自动取消的订单
	 * @param s
	 * @return
	 */
	@XxlJob("orderCancelJobHandler")
	public ReturnT<String> orderCancelJob(String s) {
		//查出所有租户
		List<SysTenant> listSysTenant = feignTenantService.list(SecurityConstants.FROM_IN).getData();
		listSysTenant.forEach(item -> {
			TenantContextHolder.setTenantId(item.getId());
			//查询出各租户下未正常自动取消的订单
			List<OrderInfo> listOrderInfo = orderInfoService.list(Wrappers.<OrderInfo>query().lambda()
					.isNull(OrderInfo::getStatus)
					.lt(OrderInfo::getCreateTime, LocalDateTime.now().minusMinutes(MallConstants.ORDER_TIME_OUT_0)));
			if(listOrderInfo != null && listOrderInfo.size() > 0){
				listOrderInfo.forEach(orderInfo -> {
					orderInfoService.orderCancel(orderInfo);
				});
			}
			TenantContextHolder.clear();
		});
		return SUCCESS;
	}

}