/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.service.GoodsAppraisesService;
import com.joolun.cloud.mall.api.service.OrderInfoService;
import com.joolun.cloud.mall.api.service.UserInfoService;
import com.joolun.cloud.mall.common.constant.MyReturnCode;
import com.joolun.cloud.mall.common.entity.GoodsAppraises;
import com.joolun.cloud.mall.common.entity.OrderInfo;
import com.joolun.cloud.mall.common.entity.UserInfo;
import com.joolun.cloud.mall.common.enums.OrderInfoEnum;
import com.joolun.cloud.weixin.common.entity.ThirdSession;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * 商品评价
 *
 * <AUTHOR>
 * @date 2019-09-23 15:51:01
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsappraises")
@Api(value = "goodsappraises", tags = "商品评价Api")
public class GoodsAppraisesController {

	private final OrderInfoService orderInfoService;
    private final GoodsAppraisesService goodsAppraisesService;
	private final UserInfoService userInfoService;

	/**
    * 分页查询
    * @param page 分页对象
    * @param goodsAppraises 商品评价
    * @return
    */
	@ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public R getGoodsAppraisesPage(Page page, GoodsAppraises goodsAppraises) {
        return R.ok(goodsAppraisesService.page2(page,goodsAppraises));
    }

    /**
    * 通过id查询商品评价
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询商品评价")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id){
        return R.ok(goodsAppraisesService.getById(id));
    }

    /**
    * 新增商品评价
    * @param listGoodsAppraises 商品评价
    * @return R
    */
	@ApiOperation(value = "新增商品评价")
    @PostMapping
	@ApiLogin
    public R save(@RequestBody List<GoodsAppraises> listGoodsAppraises){
		ThirdSession thirdSession = ThirdSessionHolder.getThirdSession();
		OrderInfo orderInfo = orderInfoService.getById(listGoodsAppraises.get(0).getOrderId());
		if(!OrderInfoEnum.STATUS_3.getValue().equals(orderInfo.getStatus())){
			return R.failed(MyReturnCode.ERR_70001.getCode(), MyReturnCode.ERR_70001.getMsg());
		}
		UserInfo userInfo = userInfoService.getById(thirdSession.getUserId());
		listGoodsAppraises.forEach(goodsAppraises -> {
			goodsAppraises.setShopId(orderInfo.getShopId());
			goodsAppraises.setUserId(userInfo.getId());
			goodsAppraises.setHeadimgUrl(userInfo.getHeadimgUrl());
			goodsAppraises.setNickName(userInfo.getNickName());
			if(StrUtil.isNotBlank(goodsAppraises.getParentId())){
				int count = goodsAppraisesService.count(Wrappers.<GoodsAppraises>lambdaQuery()
						.eq(GoodsAppraises::getParentId,goodsAppraises.getParentId()));
				if(count > 0){
					throw new RuntimeException("请不要重复追评");
				}
			}
		});
		goodsAppraisesService.doAppraises(listGoodsAppraises);
        return R.ok();
    }

    /**
    * 修改商品评价
    * @param goodsAppraises 商品评价
    * @return R
    */
	@ApiOperation(value = "修改商品评价")
    @PutMapping
	@ApiLogin
    public R updateById(@RequestBody GoodsAppraises goodsAppraises){
		goodsAppraises.setUserId(ThirdSessionHolder.getUserId());
        return R.ok(goodsAppraisesService.updateById(goodsAppraises));
    }

    /**
    * 通过id删除商品评价
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id删除商品评价")
    @DeleteMapping("/{id}")
    public R removeById(@PathVariable String id){
		return R.ok(goodsAppraisesService.removeById(id));
    }

	/**
	 * 分页查询我的评论
	 * @param page 分页对象
	 * @param goodsAppraises 商品评价
	 * @return
	 */
	@ApiOperation(value = "分页查询我的评论")
	@GetMapping("/page/user")
	@ApiLogin
	public R getGoodsAppraisesPageUser(Page page, GoodsAppraises goodsAppraises) {
		goodsAppraises.setUserId(ThirdSessionHolder.getUserId());
		return R.ok(goodsAppraisesService.page1(page,goodsAppraises));
	}
}
