/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.service.GoodsSpuSpecService;
import com.joolun.cloud.mall.api.service.GoodsSpuSpecValuePicService;
import com.joolun.cloud.mall.common.entity.GoodsSpuSpec;
import com.joolun.cloud.mall.common.entity.GoodsSpuSpecValuePic;
import com.joolun.cloud.mall.common.vo.GoodsSpecLeafVO;
import com.joolun.cloud.mall.common.vo.GoodsSpecVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * spu规格
 *
 * <AUTHOR>
 * @date 2019-08-13 16:56:46
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsspuspec")
@Api(value = "goodsspuspec", tags = "spu规格接口API")
public class GoodsSpuSpecController {

    private final GoodsSpuSpecService goodsSpuSpecService;
    private final GoodsSpuSpecValuePicService goodsSpuSpecValuePicService;


	/**
	 * 获取商品规格
	 * @param goodsSpuSpec
	 * @return
	 */
	@ApiOperation(value = "获取商品规格")
	@GetMapping("/tree")
	public R getGoodsSpuSpecTree(GoodsSpuSpec goodsSpuSpec) {
		List<GoodsSpecVO> specList = goodsSpuSpecService.tree(goodsSpuSpec);

        // 如果指定了SPU，则获取该SPU下的规格值图片
        if (goodsSpuSpec != null && goodsSpuSpec.getSpuId() != null && !goodsSpuSpec.getSpuId().isEmpty()) {
            String spuId = goodsSpuSpec.getSpuId();
            List<GoodsSpuSpecValuePic> specValuePics = goodsSpuSpecValuePicService.list(
                    Wrappers.<GoodsSpuSpecValuePic>lambdaQuery()
                            .eq(GoodsSpuSpecValuePic::getSpuId, spuId)
            );

            if (specValuePics != null && !specValuePics.isEmpty()) {
                Map<String, String> picMap = specValuePics.stream()
                        .collect(Collectors.toMap(
                                GoodsSpuSpecValuePic::getSpecValueId,
                                GoodsSpuSpecValuePic::getPic,
                                (v1, v2) -> v1));

                // 遍历规格列表，为规格值添加图片
                for (GoodsSpecVO spec : specList) {
                    if (spec.getLeaf() != null && !spec.getLeaf().isEmpty()) {
                        for (GoodsSpecLeafVO leafValue : spec.getLeaf()) {
                            if (picMap.containsKey(leafValue.getId())) {
                                // 在GoodsSpecLeafVO中添加pic字段
                                leafValue.setPic(picMap.get(leafValue.getId()));
                            }
                        }
                    }
                }
            }
        }

		return R.ok(specList);
	}

}
