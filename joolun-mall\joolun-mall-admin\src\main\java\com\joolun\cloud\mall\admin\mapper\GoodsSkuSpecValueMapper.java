/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joolun.cloud.mall.common.entity.GoodsSkuSpecValue;
import com.joolun.cloud.mall.common.entity.GoodsSpuSpec;
import com.joolun.cloud.mall.common.entity.GoodsSpec;

import java.util.List;

/**
 * 商品sku规格值
 *
 * <AUTHOR>
 * @date 2019-08-13 10:19:09
 */
public interface GoodsSkuSpecValueMapper extends BaseMapper<GoodsSkuSpecValue> {

	List<GoodsSkuSpecValue> listGoodsSkuSpecValueBySkuId(String skuId);

	List<GoodsSpec> selectBySpecId(GoodsSpuSpec goodsSpuSpec);
}
