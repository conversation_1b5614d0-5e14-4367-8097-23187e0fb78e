<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.DistributionOrderMapper">

    <resultMap id="distributionOrderMap" type="com.joolun.cloud.mall.common.entity.DistributionOrder">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="distributionLevel" column="distribution_level"/>
        <result property="distributionUserId" column="distribution_user_id"/>
        <result property="orderId" column="order_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="userId" column="user_id"/>
        <result property="commission" column="commission"/>
        <result property="commissionStatus" column="commission_status"/>
    </resultMap>

	<resultMap id="distributionOrderMap1" extends="distributionOrderMap" type="com.joolun.cloud.mall.common.entity.DistributionOrder">
		<collection property="userInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=user_id}">
		</collection>
		<collection property="orderInfo" ofType="com.joolun.cloud.mall.common.entity.OrderInfo"
					select="com.joolun.cloud.mall.admin.mapper.OrderInfoMapper.selectById"
					column="{id=order_id}">
		</collection>
	</resultMap>

    <sql id="distributionOrderSql">
        distribution_order.`id`,
        distribution_order.`tenant_id`,
        distribution_order.`del_flag`,
        distribution_order.`create_time`,
        distribution_order.`update_time`,
        distribution_order.`distribution_level`,
        distribution_order.`distribution_user_id`,
        distribution_order.`order_id`,
        distribution_order.`shop_id`,
        distribution_order.`user_id`,
        distribution_order.`commission`,
        distribution_order.`commission_status`
    </sql>

	<select id="selectPage1" resultMap="distributionOrderMap1">
		SELECT
		<include refid="distributionOrderSql"/>
		FROM distribution_order distribution_order
		<where>
			<if test="query.distributionLevel != null">
				AND distribution_order.`distribution_level` = #{query.distributionLevel}
			</if>
			<if test="query.distributionUserId != null">
				AND distribution_order.`distribution_user_id` = #{query.distributionUserId}
			</if>
			<if test="query.userId != null">
				AND distribution_order.`user_id` = #{query.userId}
			</if>
			<if test="query.orderId != null">
				AND distribution_order.`order_id` = #{query.orderId}
			</if>
			<if test="query.shopId != null">
				AND distribution_order.`shop_id` = #{query.shopId}
			</if>
			<if test="query.commissionStatus != null">
				AND distribution_order.`commission_status` = #{query.commissionStatus}
			</if>
			<if test="query.beginTime != null">
				AND distribution_order.`create_time` >= #{query.beginTime}
			</if>
			<if test="query.endTime != null">
				AND #{query.endTime} >= distribution_order.`create_time`
			</if>
		</where>
	</select>

	<select id="getDistributionUserFrozenAmount" resultType="java.math.BigDecimal">
		SELECT
		  IFNULL(SUM(distribution_order.`commission`), 0)
		FROM
		  distribution_order distribution_order
		WHERE distribution_order.`commission_status`='1'
		AND distribution_order.`distribution_user_id` = #{distributionUserId}
	</select>
</mapper>
