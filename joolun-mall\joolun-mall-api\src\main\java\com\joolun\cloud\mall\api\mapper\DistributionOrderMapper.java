/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joolun.cloud.mall.common.entity.DistributionOrder;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 分销订单
 *
 * <AUTHOR>
 * @date 2021-04-30 10:26:30
 */
public interface DistributionOrderMapper extends BaseMapper<DistributionOrder> {

	IPage<DistributionOrder> selectPage1(IPage<DistributionOrder> page, @Param("query") DistributionOrder distributionOrder);

	/**
	 * 获取分销员的冻结金额
	 * @param distributionUserId
	 * @return
	 */
	BigDecimal getDistributionUserFrozenAmount(String distributionUserId);
}
