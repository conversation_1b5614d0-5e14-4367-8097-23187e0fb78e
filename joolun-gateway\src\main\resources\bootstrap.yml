server:
  port: 9999

spring:
  application:
    name: @artifactId@
  # 配置中心
  cloud:
    nacos:
      discovery:
        server-addr: joolun-nacos:8848
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs[0]:
          data-id: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          refresh: true
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 127.0.0.1:9090
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            ### nacos连接地址
            server-addr: joolun-nacos:8848
            dataId: sentinal-joolun
            groupId: SENTINEL_GROUP
            data-type: json
            rule-type: flow
  profiles:
    active: dev