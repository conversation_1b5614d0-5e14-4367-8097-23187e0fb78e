<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="${package}.${moduleName}.mapper.${className}Mapper">

    <resultMap id="${classname}Map" type="${package}.${moduleName}.entity.${className}">
#foreach($column in $columns)
#if($column.lowerAttrName==$pk.lowerAttrName)
        <id property="${pk.lowerAttrName}" column="${pk.columnName}"/>
#else
        <result property="${column.lowerAttrName}" column="${column.columnName}"/>
#end
#end
    </resultMap>

    <sql id="${classname}Sql">
#foreach($column in $columns)
#if($column.lowerAttrName==$pk.lowerAttrName)
        $tableName.`${pk.columnName}`#if($velocityCount != $columns.size()),#end
#else
        $tableName.`${column.columnName}`#if($velocityCount != $columns.size()),#end
#end
#end
    </sql>
</mapper>
