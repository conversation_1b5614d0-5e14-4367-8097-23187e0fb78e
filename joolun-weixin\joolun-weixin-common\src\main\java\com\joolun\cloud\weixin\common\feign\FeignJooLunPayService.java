package com.joolun.cloud.weixin.common.feign;

import com.alipay.easysdk.payment.common.models.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.easysdk.payment.common.models.AlipayTradeQueryResponse;
import com.alipay.easysdk.payment.common.models.AlipayTradeRefundResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayOrderQueryRequest;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.constant.ServiceNameConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.weixin.common.dto.AliBaseRequest;
import com.joolun.cloud.weixin.common.entity.PayConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * TODO：这个重复了 8月上线以后可以考虑删掉
 */

@FeignClient(contextId = "weixinApiFeignJooLunPayService", value = ServiceNameConstants.JOOLUN_PAY_API)
public interface FeignJooLunPayService {
	/**
	 * 获取支付配置
	 * @param appId
	 * @return
	 */
	@GetMapping("/payconfig/appid/{appId}")
	R<PayConfig> getPayConfig(@PathVariable("appId") String appId, @RequestHeader(SecurityConstants.FROM) String from);
	/**
	 * 调用统一下单接口，并组装生成支付所需参数对象（微信支付）
	 *
	 * @param request 统一下单请求参数
	 * @return 返回 {@link com.github.binarywang.wxpay.bean.order}包下的类对象
	 */
	@PostMapping("/wxpay/unifiedOrder")
	R unifiedOrderWx(@RequestBody WxPayUnifiedOrderRequest request, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 处理支付回调数据（微信支付）
	 * @param xmlData
	 * @return
	 */
	@PostMapping("/wxpay/notifyOrder")
	R<WxPayOrderNotifyResult> notifyOrderWx(@RequestBody String xmlData, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * <pre>
	 * 申请退款（微信支付）
	 * 详见 https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_4
	 * 接口链接：https://api.mch.weixin.qq.com/secapi/pay/refund
	 * </pre>
	 *
	 * @param request 请求对象
	 * @return 退款操作结果 wx pay refund result
	 * @throws WxPayException the wx pay exception
	 */
	@PostMapping("/wxpay/refundOrder")
	R<WxPayRefundResult> refundOrderWx(@RequestBody WxPayRefundRequest request, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 退款回调（微信支付）
	 * @param xmlData
	 * @param from
	 * @return
	 */
	@PostMapping("/wxpay/notifyRefunds")
	R<WxPayRefundNotifyResult> notifyRefundsWx(@RequestBody String xmlData, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * <pre>
	 * 查询订单（微信支付）
	 * （适合于需要自定义子商户号和子商户appid的情形）.
	 * 详见https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_2
	 * 该接口提供所有微信支付订单的查询，商户可以通过查询订单接口主动查询订单状态，完成下一步的业务逻辑。
	 * 需要调用查询接口的情况：
	 * ◆ 当商户后台、网络、服务器等出现异常，商户系统最终未接收到支付通知；
	 * ◆ 调用支付接口后，返回系统错误或未知交易状态情况；
	 * ◆ 调用被扫支付API，返回USERPAYING的状态；
	 * ◆ 调用关单或撤销接口API之前，需确认支付状态；
	 * 接口地址：https://api.mch.weixin.qq.com/pay/orderquery
	 * </pre>
	 *
	 * @param request 查询订单请求对象
	 * @return the wx pay order query result
	 * @throws WxPayException the wx pay exception
	 */
	@PostMapping("/wxpay/queryOrder")
	R<WxPayOrderQueryResult> queryOrderWx(@RequestBody WxPayOrderQueryRequest request, @RequestHeader(SecurityConstants.FROM) String from);


	/**
	 * 调用统一下单接口（支付宝）
	 *
	 * @param aliBaseRequest 统一下单请求参数
	 * @return
	 */
	@PostMapping("/alipay/unifiedOrder")
	R unifiedOrderAli(@RequestBody AliBaseRequest aliBaseRequest, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 查询订单（支付宝）
	 * @param aliBaseRequest
	 * @param from
	 * @return
	 */
	@PostMapping("/alipay/queryOrder")
	R<AlipayTradeQueryResponse> queryOrderAli(@RequestBody AliBaseRequest aliBaseRequest, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 申请退款（支付宝）
	 * @param aliBaseRequest
	 * @param from
	 * @return
	 */
	@PostMapping("/alipay/refundOrder")
	R<AlipayTradeRefundResponse> refundOrderAli(@RequestBody AliBaseRequest aliBaseRequest, @RequestHeader(SecurityConstants.FROM) String from);

	/**
	 * 查询退款（支付宝）
	 * @param aliBaseRequest
	 * @param from
	 * @return
	 */
	@PostMapping("/alipay/queryRefundOrder")
	R<AlipayTradeFastpayRefundQueryResponse> queryRefundOrder(@RequestBody AliBaseRequest aliBaseRequest, @RequestHeader(SecurityConstants.FROM) String from);
}
