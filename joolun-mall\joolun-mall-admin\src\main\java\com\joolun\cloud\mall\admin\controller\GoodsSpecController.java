/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.GoodsSpec;
import com.joolun.cloud.mall.admin.service.GoodsSpecService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;

/**
 * 规格
 *
 * <AUTHOR>
 * @date 2019-08-13 16:10:54
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsspec")
@Api(value = "goodsspec", tags = "规格管理")
public class GoodsSpecController {

    private final GoodsSpecService goodsSpecService;

    /**
    * 分页查询
    * @param page 分页对象
    * @param goodsSpec 规格
    * @return
    */
	@ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspec:index')")
    public R getGoodsSpecPage(Page page, GoodsSpec goodsSpec) {
        return R.ok(goodsSpecService.page(page,Wrappers.query(goodsSpec)));
    }

	@ApiOperation(value = "list查询")
	@GetMapping("/list")
	public R getGoodsSpecList(GoodsSpec goodsSpec) {
		return R.ok(goodsSpecService.list(Wrappers.query(goodsSpec)));
	}

    /**
    * 通过id查询规格
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询规格")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspec:get')")
    public R getById(@PathVariable("id") String id){
        return R.ok(goodsSpecService.getById(id));
    }

    /**
    * 新增规格
    * @param goodsSpec 规格
    * @return R
    */
	@ApiOperation(value = "新增规格")
    @SysLog("新增规格")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:index')")
    public R save(@RequestBody GoodsSpec goodsSpec){
		try {
			goodsSpecService.save(goodsSpec);
			return R.ok(goodsSpec);
		}catch (DuplicateKeyException e){
			if(e.getMessage().contains("uk_name")){
				return R.failed("该规格已存在");
			}else{
				return R.failed(e.getMessage());
			}
		}
    }

    /**
    * 修改规格
    * @param goodsSpec 规格
    * @return R
    */
	@ApiOperation(value = "修改规格")
    @SysLog("修改规格")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspec:edit')")
    public R updateById(@RequestBody GoodsSpec goodsSpec){
		try {
        	return R.ok(goodsSpecService.updateById(goodsSpec));
		}catch (DuplicateKeyException e){
			if(e.getMessage().contains("uk_name")){
				return R.failed("该规格已存在");
			}else{
				return R.failed(e.getMessage());
			}
		}
    }

    /**
    * 通过id删除规格
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id删除规格")
    @SysLog("删除规格")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspec:del')")
    public R removeById(@PathVariable String id){
        return R.ok(goodsSpecService.removeById(id));
    }

}
