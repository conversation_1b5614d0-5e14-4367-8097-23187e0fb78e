/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.service.SignRecordService;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 签到记录
 *
 * <AUTHOR>
 * @date 2021-01-13 15:29:30
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/signrecord")
@Api(value = "signrecord", tags = "签到记录API")
public class SignRecordController {

    private final SignRecordService signRecordService;

    /**
     * 签到记录查询
     * @return R
     */
    @ApiOperation(value = "签到记录查询")
    @GetMapping("/user")
	@ApiLogin
    public R getById() {
        return R.ok(signRecordService.getSignRecord(ThirdSessionHolder.getUserId()));
    }

	/**
	 * 用户签到
	 * @return R
	 */
	@ApiOperation(value = "用户签到")
	@PostMapping("/user")
	@ApiLogin
	public R userSign(){
		return R.ok(signRecordService.userSign(ThirdSessionHolder.getUserId()));
	}
}
