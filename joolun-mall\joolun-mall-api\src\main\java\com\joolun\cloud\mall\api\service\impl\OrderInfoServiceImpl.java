/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.easysdk.payment.common.models.AlipayTradeQueryResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.request.WxPayOrderQueryRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.LocalDateTimeUtils;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.mall.api.mapper.*;
import com.joolun.cloud.mall.api.service.*;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.dto.PlaceOrderDTO;
import com.joolun.cloud.mall.common.dto.PlaceOrderSkuDTO;
import com.joolun.cloud.mall.common.entity.*;
import com.joolun.cloud.mall.common.enums.OrderItemEnum;
import com.joolun.cloud.mall.common.enums.OrderLogisticsEnum;
import com.joolun.cloud.mall.common.enums.OrderInfoEnum;
import com.joolun.cloud.mall.common.feign.FeignJooLunPayService;
import com.joolun.cloud.mall.common.feign.FeignWxTemplateMsgService;
import com.joolun.cloud.pay.common.constant.PayConstants;
import com.joolun.cloud.pay.common.dto.AliBaseRequest;
import com.joolun.cloud.weixin.common.constant.ConfigConstant;
import com.joolun.cloud.weixin.common.dto.WxTemplateMsgSendDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderInfoServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfo> implements OrderInfoService {

	private final GoodsSkuMapper goodsSkuMapper;
	private final GoodsSpuService goodsSpuService;
	private final GoodsSkuService goodsSkuService;
	private final OrderItemService orderItemService;
	private final GoodsSkuSpecValueMapper goodsSkuSpecValueMapper;
	private final ShoppingCartService shoppingCartService;
	private final OrderLogisticsService orderLogisticsService;
	private final UserAddressService userAddressService;
	private final RedisTemplate<String, String> redisTemplate;
	private final OrderLogisticsDetailService orderLogisticsDetailService;
	private final PointsRecordService pointsRecordService;
	private final UserInfoService userInfoService;
	private final FeignJooLunPayService feignJooLunPayService;
	private final CouponUserService couponUserService;
	private final BargainUserMapper bargainUserMapper;
	private final GrouponInfoMapper grouponInfoMapper;
	private final GrouponUserMapper grouponUserMapper;
	private final FeignWxTemplateMsgService feignWxTemplateMsgService;
	private final ShopInfoService shopInfoService;
	private final SeckillInfoService seckillInfoService;
	private final UserShopService userShopService;
	private final UserRecordService userRecordService;
	private final DistributionConfigService distributionConfigService;
	private final DistributionUserService distributionUserService;
	private final DistributionOrderService distributionOrderService;
	private final DistributionOrderItemService distributionOrderItemService;
	private final BargainCutService bargainCutService;

	@Override
	public IPage<OrderInfo> page2(IPage<OrderInfo> page, OrderInfo orderInfo) {
		return baseMapper.selectPage2(page,orderInfo);
	}

	@Override
	public OrderInfo getById2(Serializable id) {
		OrderInfo orderInfo = baseMapper.selectById2(id);
		if(orderInfo != null){
			String keyRedis = null;
			//获取自动取消倒计时
			if(CommonConstants.NO.equals(orderInfo.getIsPay())){
				keyRedis = String.valueOf(StrUtil.format("{}{}:{}",MallConstants.REDIS_ORDER_KEY_IS_PAY_0, TenantContextHolder.getTenantId(),orderInfo.getId()));
			}
			//获取自动收货倒计时
			if(OrderInfoEnum.STATUS_2.getValue().equals(orderInfo.getStatus())){
				keyRedis = String.valueOf(StrUtil.format("{}{}:{}",MallConstants.REDIS_ORDER_KEY_STATUS_2, TenantContextHolder.getTenantId(),orderInfo.getId()));
			}
			if(keyRedis != null){
				Long outTime = redisTemplate.getExpire(keyRedis);
				if(outTime != null && outTime > 0){
					orderInfo.setOutTime(outTime);
				}
			}
		}
		return orderInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void orderCancel(OrderInfo orderInfo) {
		if(CommonConstants.NO.equals(orderInfo.getIsPay()) && !OrderInfoEnum.STATUS_5.getValue().equals(orderInfo.getStatus())){//校验
			//实时查询订单是否已支付
			WxPayOrderQueryRequest request = new WxPayOrderQueryRequest();
			ShopInfo shopInfo = shopInfoService.getById(orderInfo.getShopId());
			if(shopInfo != null){
				//微信支付
				if(StrUtil.isNotBlank(shopInfo.getWxMchId())){
					request.setTransactionId(orderInfo.getTransactionId());
					request.setOutTradeNo(orderInfo.getOrderNo());
					request.setSubMchId(shopInfo.getWxMchId());
					R<WxPayOrderQueryResult> r = feignJooLunPayService.queryOrderWx(request, SecurityConstants.FROM_IN);
					if(r.isOk()){
						WxPayOrderQueryResult wxPayOrderQueryResult = r.getData();
						if(!WxPayConstants.WxpayTradeStatus.NOTPAY.equals(wxPayOrderQueryResult.getTradeState())){//只有未支付的订单能取消
							throw new RuntimeException("该订单已支付，不能取消");
						}
					}
				}
				//支付宝
				if(StrUtil.isNotBlank(shopInfo.getAliAuthToken())){
					AliBaseRequest aliBaseRequest = new AliBaseRequest();
					Map<String, Object> params = new HashMap<>();
					params.put("outTradeNo",orderInfo.getOrderNo());
					aliBaseRequest.setAppAuthToken(shopInfo.getAliAuthToken());
					aliBaseRequest.setParams(params);
					R<AlipayTradeQueryResponse> r = feignJooLunPayService.queryOrderAli(aliBaseRequest, SecurityConstants.FROM_IN);
					if(r.isOk()){
						AlipayTradeQueryResponse alipayTradeQueryResponse = r.getData();
						if(PayConstants.ALI_RES_CODE_SUCCESS.equals(alipayTradeQueryResponse.getCode())
								&& ("TRADE_SUCCESS".equals(alipayTradeQueryResponse.getTradeStatus())
								||"TRADE_FINISHED".equals(alipayTradeQueryResponse.getTradeStatus()))){//只有未支付的订单能取消
							throw new RuntimeException("该订单已支付，不能取消");
						}
					}
				}
			}
			orderInfo.setStatus(OrderInfoEnum.STATUS_5.getValue());
			//回滚库存
			List<OrderItem> listOrderItem = orderItemService.list(Wrappers.<OrderItem>lambdaQuery()
					.eq(OrderItem::getOrderId,orderInfo.getId()));
			listOrderItem.forEach(orderItem -> {
				GoodsSku goodsSku = goodsSkuMapper.selectById(orderItem.getSkuId());
				if(goodsSku != null){
					goodsSku.setStock(goodsSku.getStock() + orderItem.getQuantity());
					if(!goodsSkuService.updateById(goodsSku)){//更新库存
						throw new RuntimeException("请重新提交");
					}
				}
				//回滚积分
				PointsRecord pointsRecord = new PointsRecord();
				pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_4);
				pointsRecord.setOrderItemId(orderItem.getId());
				pointsRecord = pointsRecordService.getOne(Wrappers.query(pointsRecord));
				//查询该订单详情是否有抵扣积分
				if(pointsRecord !=null && StrUtil.isNotBlank(pointsRecord.getId())){
					//减回赠送的积分
					pointsRecord.setId(null);
					pointsRecord.setTenantId(null);
					pointsRecord.setCreateTime(null);
					pointsRecord.setUpdateTime(null);
					pointsRecord.setShopId(orderItem.getShopId());
					pointsRecord.setDescription("【订单取消】 " + pointsRecord.getDescription());
					pointsRecord.setAmount(- pointsRecord.getAmount());
					pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_5);
					//新增积分记录
					pointsRecordService.save(pointsRecord);
					//减去赠送积分
					userInfoService.updatePoints(orderInfo.getUserId(),pointsRecord.getAmount());
				}
				//回滚秒杀已售数量
				if(MallConstants.ORDER_TYPE_3.equals(orderInfo.getOrderType())){
					SeckillInfo seckillInfo = seckillInfoService.getById(orderInfo.getMarketId());
					seckillInfo.setSeckillNum(seckillInfo.getSeckillNum()-1);
					if(!seckillInfoService.updateById(seckillInfo)){//更新秒杀已售数量
						throw new RuntimeException("请重新提交");
					}
				}
			});
			baseMapper.updateById(orderInfo);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void orderReceive(OrderInfo orderInfo) {
		orderInfo.setStatus(OrderInfoEnum.STATUS_3.getValue());
		orderInfo.setAppraisesStatus(MallConstants.APPRAISES_STATUS_0);
		orderInfo.setReceiverTime(LocalDateTime.now());
		baseMapper.updateById(orderInfo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeById(Serializable id) {
		orderItemService.remove(Wrappers.<OrderItem>lambdaQuery()
				.eq(OrderItem::getOrderId,id));//删除订单详情
		return super.removeById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<OrderInfo> orderSub(PlaceOrderDTO placeOrderDTO, SeckillInfo seckillInfo) {
		List<OrderInfo> rs = new ArrayList<>();
		Map<String, List<PlaceOrderSkuDTO>> mapSkuDTO = placeOrderDTO.getSkus().stream().collect(Collectors.groupingBy(PlaceOrderSkuDTO::getSkuId));

		//查询出所有sku信息
		List<GoodsSku> listSku = goodsSkuMapper.listSkuInOrderSub(placeOrderDTO.getSkus());
		//通过店铺分组
		Map<String, List<GoodsSku>> goodsSkuMap = listSku.stream().collect(Collectors.groupingBy(GoodsSku::getShopId));
		goodsSkuMap.forEach((key, value) -> {
			//判断是否已经是店铺用户，否则加入listUserShop新增
			UserShop userShop = new UserShop();
			userShop.setShopId(value.get(0).getShopId());
			userShop.setUserId(placeOrderDTO.getUserId());
			int count = userShopService.count(Wrappers.query(userShop));
			if(count <= 0){
				userShopService.save(userShop);
			}

			OrderInfo orderInfo = new OrderInfo();
			BeanUtil.copyProperties(placeOrderDTO,orderInfo);
			orderInfo.setShopId(value.get(0).getShopId());
			orderInfo.setIsPay(CommonConstants.NO);
			orderInfo.setOrderNo(IdUtil.getSnowflake(0,0).nextIdStr());
			orderInfo.setSalesPrice(BigDecimal.ZERO);
			orderInfo.setFreightPrice(BigDecimal.ZERO);
			orderInfo.setPaymentPoints(0);
			orderInfo.setPaymentPrice(BigDecimal.ZERO);
			orderInfo.setPaymentCouponPrice(BigDecimal.ZERO);
			orderInfo.setPaymentPointsPrice(BigDecimal.ZERO);
			orderInfo.setCreateTime(LocalDateTime.now());

			List<OrderItem> listOrderItem = new ArrayList<>();
			List<GoodsSku> listGoodsSku = new ArrayList<>();
			List<CouponUser> listCouponUser = new ArrayList<>();
			List<PointsRecord> listPointsRecord = new ArrayList<>();
			value.forEach(goodsSku -> {
				List<PlaceOrderSkuDTO> listPlaceOrderSkuDTO = mapSkuDTO.get(goodsSku.getId());
				if(listPlaceOrderSkuDTO.size() != 1){
					throw new RuntimeException("参数错误");
				}
				PlaceOrderSkuDTO placeOrderSkuDTO = listPlaceOrderSkuDTO.get(0);
                GoodsSpu goodsSpu = goodsSku.getGoodsSpu();
                //只有已上架和审核通过的商品能下单
                if(goodsSpu != null && CommonConstants.YES.equals(goodsSpu.getShelf()) && MallConstants.GOODS_STATUS_1.equals(goodsSpu.getVerifyStatus())){
                    OrderItem orderItem = new OrderItem();
                    orderItem.setOrderId(orderInfo.getId());
                    orderItem.setShopId(orderInfo.getShopId());
                    orderItem.setStatus(OrderItemEnum.STATUS_0.getValue());
                    orderItem.setIsRefund(CommonConstants.NO);
                    orderItem.setSpuId(goodsSpu.getId());
                    orderItem.setSkuId(goodsSku.getId());
                    orderItem.setSpuName(goodsSpu.getName());
                    orderItem.setPicUrl(StrUtil.isNotBlank(goodsSku.getPicUrl()) ? goodsSku.getPicUrl() : goodsSpu.getPicUrls()[0]);
                    orderItem.setQuantity(placeOrderSkuDTO.getQuantity());
                    orderItem.setSalesPrice(goodsSku.getSalesPrice());
                    if(MallConstants.DELIVERY_WAY_1.equals(orderInfo.getDeliveryWay())){//快递配送要算运费
                        orderItem.setFreightPrice(placeOrderSkuDTO.getFreightPrice());
                    }else{//自提配送不算运费
                        orderItem.setFreightPrice(BigDecimal.ZERO);
                    }

                    orderItem.setPaymentPrice(placeOrderSkuDTO.getPaymentPrice().add(orderItem.getFreightPrice()));
                    orderItem.setPaymentPoints(placeOrderSkuDTO.getPaymentPoints());
                    orderItem.setPaymentCouponPrice(placeOrderSkuDTO.getPaymentCouponPrice());
                    orderItem.setPaymentPointsPrice(placeOrderSkuDTO.getPaymentPointsPrice());
                    orderItem.setCouponUserId(placeOrderSkuDTO.getCouponUserId());

                    //校验参数（数量、金额）合法性
                    verifyOrderItem(orderInfo, orderItem, goodsSku);

                    BigDecimal quantity = new BigDecimal(placeOrderSkuDTO.getQuantity());
                    if(StrUtil.isNotBlank(orderItem.getCouponUserId())){
                        //校验电子券
                        CouponUser couponUser = couponUserService.getById(orderItem.getCouponUserId());
                        if(couponUser == null){
                            throw new RuntimeException("非法优惠券");
                        }
                        if(!MallConstants.COUPON_USER_STATUS_0.equals(couponUser.getStatus())){
                            throw new RuntimeException("优惠券已经使用");
                        }
                        if(couponUser.getValidBeginTime().isAfter(LocalDateTime.now())){
                            throw new RuntimeException("优惠券未在使用期");
                        }
                        if(couponUser.getValidEndTime().isBefore(LocalDateTime.now())){
                            throw new RuntimeException("优惠券已过期");
                        }
                        couponUser.setStatus(MallConstants.COUPON_USER_STATUS_1);
                        couponUser.setUsedTime(LocalDateTime.now());
                        listCouponUser.add(couponUser);
                    }

                    List<GoodsSkuSpecValue> listGoodsSkuSpecValue = goodsSkuSpecValueMapper.listGoodsSkuSpecValueBySkuId(goodsSku.getId());
                    listGoodsSkuSpecValue.forEach(goodsSkuSpecValue -> {
                        String specInfo = orderItem.getSpecInfo();
                        specInfo = StrUtil.isNotBlank(specInfo) ? specInfo : "";
                        orderItem.setSpecInfo(specInfo
                                + goodsSkuSpecValue.getSpecValueName()
                                +  "，" );
                    });
                    String specInfo = orderItem.getSpecInfo();
                    if(StrUtil.isNotBlank(specInfo)){
                        orderItem.setSpecInfo(specInfo.substring(0,specInfo.length() - 1));
                    }
                    listOrderItem.add(orderItem);
                    orderInfo.setSalesPrice(orderInfo.getSalesPrice().add(goodsSku.getSalesPrice().multiply(quantity)));
                    orderInfo.setFreightPrice(orderInfo.getFreightPrice().add(orderItem.getFreightPrice()));
                    orderInfo.setPaymentPrice(orderInfo.getPaymentPrice().add(orderItem.getPaymentPrice()));
                    orderInfo.setPaymentPoints(orderInfo.getPaymentPoints() + (orderItem.getPaymentPoints() != null ? orderItem.getPaymentPoints() : 0));
                    orderInfo.setPaymentCouponPrice(orderInfo.getPaymentCouponPrice().add((orderItem.getPaymentCouponPrice() != null ? orderItem.getPaymentCouponPrice() : BigDecimal.ZERO)));
                    orderInfo.setPaymentPointsPrice(orderInfo.getPaymentPointsPrice().add((orderItem.getPaymentPointsPrice() != null ? orderItem.getPaymentPointsPrice() : BigDecimal.ZERO)));
                    goodsSku.setStock(goodsSku.getStock() - orderItem.getQuantity());
                    listGoodsSku.add(goodsSku);
                    //删除购物车
                    shoppingCartService.remove(Wrappers.<ShoppingCart>lambdaQuery()
                            .eq(ShoppingCart::getSpuId,goodsSpu.getId())
                            .eq(ShoppingCart::getSkuId,goodsSku.getId())
                            .eq(ShoppingCart::getUserId,placeOrderDTO.getUserId()));
                }
            });
			if(!listOrderItem.isEmpty() && !listGoodsSku.isEmpty()){
				if(MallConstants.DELIVERY_WAY_1.equals(orderInfo.getDeliveryWay())){//配送方式1、普通快递的订单要新增订单物流
					UserAddress userAddress = userAddressService.getById(placeOrderDTO.getUserAddressId());
					OrderLogistics orderLogistics = new OrderLogistics();
					orderLogistics.setPostalCode(userAddress.getPostalCode());
					orderLogistics.setUserName(userAddress.getUserName());
					orderLogistics.setTelNum(userAddress.getTelNum());
					orderLogistics.setAddress(userAddress.getProvinceName()+userAddress.getCityName()+userAddress.getCountyName()+userAddress.getDetailInfo());
					//新增订单物流
					orderLogisticsService.save(orderLogistics);
					orderInfo.setLogisticsId(orderLogistics.getId());
					orderInfo.setOrderLogistics(orderLogistics);
				}

				orderInfo.setName(listOrderItem.get(0).getSpuName());
				super.save(orderInfo);//保存订单
				rs.add(orderInfo);
				listOrderItem.forEach(orderItem -> orderItem.setOrderId(orderInfo.getId()));
				//保存订单详情
				orderItemService.saveBatch(listOrderItem);
				//修改用户电子券状态
				if(!listCouponUser.isEmpty()){
					couponUserService.updateBatchById(listCouponUser);
				}
				// 积分处理
				listOrderItem.forEach(orderItem -> {
					if(orderItem.getPaymentPoints() != null && orderItem.getPaymentPoints() > 0){
						//处理积分抵扣
						PointsRecord pointsRecord = new PointsRecord();
						pointsRecord.setShopId(orderItem.getShopId());
						pointsRecord.setUserId(orderInfo.getUserId());
						pointsRecord.setDescription("【抵扣】购买商品【" + orderItem.getSpuName() + "】 * " +orderItem.getQuantity());
						pointsRecord.setSpuId(orderItem.getSpuId());
						pointsRecord.setOrderItemId(orderItem.getId());
						pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_4);
						pointsRecord.setAmount(- orderItem.getPaymentPoints());
						listPointsRecord.add(pointsRecord);
					}
				});
				//库存处理
				listGoodsSku.forEach(goodsSkuItem -> {
					if(!goodsSkuService.updateById(goodsSkuItem)){//更新库存
						throw new RuntimeException("请重新提交");
					}
				});

				//订单自动取消时间
				long orderTimeOut = MallConstants.ORDER_TIME_OUT_0;
				//秒杀订单处理
				if(MallConstants.ORDER_TYPE_3.equals(placeOrderDTO.getOrderType())){
					orderTimeOut = MallConstants.ORDER_TIME_OUT_0_SECKILL;
					seckillInfo.setSeckillNum(seckillInfo.getSeckillNum()+1);
					if(!seckillInfoService.updateById(seckillInfo)){//更新秒杀已售数量
						throw new RuntimeException("请重新提交");
					};
				}
				if(orderInfo.getPaymentPoints() > 0){
					//新增积分记录
					pointsRecordService.saveBatch(listPointsRecord);
					//更新用户积分
					UserInfo userInfo = userInfoService.getById(orderInfo.getUserId());
					if(userInfo.getPointsCurrent() < orderInfo.getPaymentPoints()){
						throw new RuntimeException("积分不足");
					}
					userInfoService.updatePoints(orderInfo.getUserId(),-orderInfo.getPaymentPoints());
				}
				//加入redis，30分钟自动取消
				String keyRedis = String.valueOf(StrUtil.format("{}{}:{}",MallConstants.REDIS_ORDER_KEY_IS_PAY_0, TenantContextHolder.getTenantId(),orderInfo.getId()));
				redisTemplate.opsForValue().set(keyRedis, orderInfo.getOrderNo() , orderTimeOut , TimeUnit.MINUTES);//设置过期时间
			}
		});
		return rs;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void notifyOrder(OrderInfo orderInfo) {
		if(CommonConstants.NO.equals(orderInfo.getIsPay())){//只有未支付订单能操作
			orderInfo.setIsPay(CommonConstants.YES);
			orderInfo.setStatus(OrderInfoEnum.STATUS_1.getValue());
			List<OrderItem> listOrderItem = orderItemService.list(Wrappers.<OrderItem>lambdaQuery()
					.eq(OrderItem::getOrderId,orderInfo.getId()));
			Map<String, List<OrderItem>> resultList = listOrderItem.stream().collect(Collectors.groupingBy(OrderItem::getSpuId));
			AtomicReference<Integer> pointsGiveAmount = new AtomicReference<>(0);
			List<PointsRecord> listPointsRecord = new ArrayList<>();
			List<GoodsSpu> listGoodsSpu = goodsSpuService.listByIds(resultList.keySet());
			listGoodsSpu.forEach(goodsSpu -> {
				resultList.get(goodsSpu.getId()).forEach(orderItem -> {
					//更新销量
					goodsSpu.setSaleNum(goodsSpu.getSaleNum()+orderItem.getQuantity());
					//处理积分赠送
					if(CommonConstants.YES.equals(goodsSpu.getPointsGiveSwitch())){
						PointsRecord pointsRecord = new PointsRecord();
						pointsRecord.setShopId(orderItem.getShopId());
						pointsRecord.setUserId(orderInfo.getUserId());
						pointsRecord.setDescription("【赠送】购买商品【" + goodsSpu.getName() + "】 * " +orderItem.getQuantity());
						pointsRecord.setSpuId(goodsSpu.getId());
						pointsRecord.setOrderItemId(orderItem.getId());
						pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_2);
						pointsRecord.setAmount(orderItem.getQuantity() * goodsSpu.getPointsGiveNum());
						listPointsRecord.add(pointsRecord);
						pointsGiveAmount.updateAndGet(v -> v + orderItem.getQuantity() * goodsSpu.getPointsGiveNum());
					}
				});
				goodsSpuService.updateById(goodsSpu);
			});
			//新增积分记录
			pointsRecordService.saveBatch(listPointsRecord);
			//更新用户积分
			userInfoService.updatePoints(orderInfo.getUserId(),pointsGiveAmount.get());

			//更新用户消费记录：总计订单数、总计消费金额
			UserRecord userRecord = userRecordService.getById(orderInfo.getUserId());
			//判断是否已有消费记录，没有则先新增
			if(userRecord == null){
				userRecord = new UserRecord();
				userRecord.setUserId(orderInfo.getUserId());
				userRecordService.save(userRecord);
			}
			userRecordService.updateTotalAmount(orderInfo.getUserId(),orderInfo.getPaymentPrice());
			userRecordService.updateTotalOrder(orderInfo.getUserId(),1);

			//处理砍价订单，更新砍价记录
			if(MallConstants.ORDER_TYPE_1.equals(orderInfo.getOrderType())){
				BargainUser bargainUser = bargainUserMapper.selectById(orderInfo.getRelationId());
				bargainUser.setIsBuy(CommonConstants.YES);
				bargainUser.setOrderId(orderInfo.getId());
				bargainUserMapper.updateById(bargainUser);
			}
			//处理拼团订单
			if(MallConstants.ORDER_TYPE_2.equals(orderInfo.getOrderType())){
				GrouponInfo grouponInfo = grouponInfoMapper.selectById(orderInfo.getMarketId());
				UserInfo userInfo = userInfoService.getById(orderInfo.getUserId());
				//新增拼团记录
				GrouponUser grouponUser = new GrouponUser();
				grouponUser.setShopId(grouponInfo.getShopId());
				grouponUser.setGrouponId(grouponInfo.getId());
				grouponUser.setGroupId(orderInfo.getMarketId());
				grouponUser.setUserId(orderInfo.getUserId());
				grouponUser.setNickName(userInfo.getNickName());
				grouponUser.setHeadimgUrl(userInfo.getHeadimgUrl());
				grouponUser.setSpuId(grouponInfo.getSpuId());
				grouponUser.setSkuId(grouponInfo.getSkuId());
				grouponUser.setGrouponNum(grouponInfo.getGrouponNum());
				grouponUser.setGrouponPrice(grouponInfo.getGrouponPrice());
				grouponUser.setStatus(MallConstants.GROUPON_USER_STATUS_0);
				grouponUserMapper.insert(grouponUser);
				if(StrUtil.isBlank(orderInfo.getRelationId())){//新团，当前用户为团长
					grouponUser.setValidBeginTime(orderInfo.getPaymentTime());
					grouponUser.setValidEndTime(orderInfo.getPaymentTime().plusHours(grouponInfo.getDuration()));
					grouponUser.setIsLeader(CommonConstants.YES);
					grouponUser.setGroupId(grouponUser.getId());
					orderInfo.setRelationId(grouponUser.getId());
					orderInfo.setStatus(OrderInfoEnum.STATUS_10.getValue());
				}else{
					grouponUser.setGroupId(orderInfo.getRelationId());
					grouponUser.setIsLeader(CommonConstants.NO);
					//查出团长拼团记录
					GrouponUser grouponUser2 = grouponUserMapper.selectById(orderInfo.getRelationId());
					grouponUser.setValidBeginTime(grouponUser2.getValidBeginTime());
					grouponUser.setValidEndTime(grouponUser2.getValidEndTime());
					Integer count = grouponUserMapper.selectCountGrouponing(orderInfo.getRelationId()) + 1;
					if(count == grouponUser2.getGrouponNum()){//已满成团
						GrouponUser grouponUser3 = new GrouponUser();
						grouponUser.setStatus(MallConstants.GROUPON_USER_STATUS_1);
						grouponUser3.setStatus(MallConstants.GROUPON_USER_STATUS_1);
						grouponUserMapper.update(grouponUser3, Wrappers.<GrouponUser>lambdaQuery()
								.eq(GrouponUser::getGroupId, grouponUser2.getId()));
						//更新团里所有订单状态
						OrderInfo orderInfo1 = new OrderInfo();
						orderInfo1.setStatus(OrderInfoEnum.STATUS_1.getValue());
						baseMapper.update(orderInfo1, Wrappers.<OrderInfo>lambdaQuery()
								.eq(OrderInfo::getRelationId, grouponUser2.getId())
								.eq(OrderInfo::getStatus, OrderInfoEnum.STATUS_10.getValue()));
					}else if(count > grouponUser2.getGrouponNum()){//满团开新团
						grouponUser.setValidBeginTime(orderInfo.getPaymentTime());
						grouponUser.setValidEndTime(orderInfo.getPaymentTime().plusHours(grouponInfo.getDuration()));
						grouponUser.setIsLeader(CommonConstants.YES);
						grouponUser.setGroupId(grouponUser.getId());
						orderInfo.setRelationId(grouponUser.getId());
						orderInfo.setStatus(OrderInfoEnum.STATUS_10.getValue());
					}else{//未满入团
						orderInfo.setStatus(OrderInfoEnum.STATUS_10.getValue());
					}
				}
				grouponUser.setOrderId(orderInfo.getId());
				grouponUserMapper.updateById(grouponUser);
				grouponInfo.setLaunchNum(grouponInfo.getLaunchNum()+1);//参与人数+1
				grouponInfoMapper.updateById(grouponInfo);
			}
			baseMapper.updateById(orderInfo);//更新订单

			if(MallConstants.ORDER_TYPE_0.equals(orderInfo.getOrderType())){//普通订单才参与分销
				//分销逻辑
				DistributionConfig distributionConfig = distributionConfigService.getOne(Wrappers.emptyWrapper());
				if(distributionConfig != null && CommonConstants.YES.equals(distributionConfig.getEnable())){//判断是否已开启分销
					//获取订单用户信息
					UserInfo userInfo = userInfoService.getById(orderInfo.getUserId());
					//如果分销模式为满额分销，在此判断当前用户是否能成为分销员
					if(MallConstants.DISTRIBUTION_MODEL_3.equals(distributionConfig.getDistributionModel())){
						//先判断当前用户是否已经成为分销员
						DistributionUser distributionUser = distributionUserService.getById(userInfo.getId());
						if(distributionUser == null){
							//还不是分销员，获取用户的消费记录
							UserRecord userRecord1 = userRecordService.getById(userInfo.getId());
							if(userRecord1 != null){
								//判断用户总计消费金额是否大于等于指定额度
								if(userRecord1.getTotalAmount().compareTo(distributionConfig.getFullAmount()) >= 0){
									//用户总计消费金额大于等于指定额度，则将当前用户设为分销员
									distributionUser = new DistributionUser();
									distributionUser.setUserId(userInfo.getId());
									distributionUserService.save(distributionUser);
								}
							}
						}
					}
					//判断订单用户是否有上级
					if(StrUtil.isNotBlank(userInfo.getParentId())){
						DistributionUser distributionUser1 = distributionUserService.getById(userInfo.getParentId());
						//判断订单用户上级是否是分销员
						if(distributionUser1 != null){
							//一级返利
							DistributionOrder distributionOrder1 = new DistributionOrder();
							distributionOrder1.setDistributionLevel(MallConstants.DISTRIBUTION_LEVEL_1);
							distributionOrder1.setDistributionUserId(distributionUser1.getUserId());
							distributionOrder1.setUserId(userInfo.getId());
							distributionOrder1.setOrderId(orderInfo.getId());
							distributionOrder1.setShopId(orderInfo.getShopId());
							distributionOrder1.setCommissionStatus(MallConstants.DISTRIBUTION_COMMISSION_STATUS_1);
							distributionOrder1.setCommission(BigDecimal.ZERO);
							List<DistributionOrderItem> listDistributionOrderItem1 = new ArrayList<>();
							listOrderItem.forEach(orderItem1 -> {
								//获取sku信息
								GoodsSku goodsSku1 = goodsSkuService.getById(orderItem1.getSkuId());
								//计算此订单明细的返利金额
								BigDecimal commission1 = BigDecimal.valueOf(goodsSku1.getFirstRebate()).divide(BigDecimal.valueOf(100)).multiply(orderItem1.getPaymentPrice());
								if(commission1.compareTo(BigDecimal.ZERO) == 1){//返利金额大于0才入库
									DistributionOrderItem distributionOrderItem1 = new DistributionOrderItem();
									distributionOrderItem1.setDistributionOrderId(distributionOrder1.getId());
									distributionOrderItem1.setOrderId(orderItem1.getOrderId());
									distributionOrderItem1.setOrderItemId(orderItem1.getId());
									distributionOrderItem1.setCommission(commission1);
									listDistributionOrderItem1.add(distributionOrderItem1);
									distributionOrder1.setCommission(distributionOrder1.getCommission().add(commission1));
								}
							});
							if(distributionOrder1.getCommission().compareTo(BigDecimal.ZERO) == 1){//返利金额大于0才入库
								//新增分销订单
								distributionOrderService.save(distributionOrder1);
								//批量新增分销订单明细
								listDistributionOrderItem1.forEach(distributionOrderItem1 -> {
									distributionOrderItem1.setDistributionOrderId(distributionOrder1.getId());
								});
								distributionOrderItemService.saveBatch(listDistributionOrderItem1);
								//更新分销员累计佣金金额
								distributionUserService.updateCommissionTotal(distributionUser1.getUserId(),distributionOrder1.getCommission());

								//加入redis，冻结时间自动解冻
								String keyRedis = String.valueOf(StrUtil.format("{}{}:{}",MallConstants.REDIS_DISTRIBUTION_ORDER_THAW, TenantContextHolder.getTenantId(), distributionOrder1.getId()));
								redisTemplate.opsForValue().set(keyRedis, distributionOrder1.getId() , distributionConfig.getFrozenTime() , TimeUnit.HOURS);//设置过期时间
							}
						}
						//获取一级用户信息
						UserInfo userInfo1 = userInfoService.getById(userInfo.getParentId());
						//判断一级用户是否有上级
						if(userInfo1 != null && StrUtil.isNotBlank(userInfo1.getParentId())){
							DistributionUser distributionUser2 = distributionUserService.getById(userInfo1.getParentId());
							//判断一级用户上级是否是分销员
							if(distributionUser2 != null){
								//二级返利
								DistributionOrder distributionOrder2 = new DistributionOrder();
								distributionOrder2.setDistributionLevel(MallConstants.DISTRIBUTION_LEVEL_2);
								distributionOrder2.setDistributionUserId(distributionUser2.getUserId());
								distributionOrder2.setUserId(userInfo.getId());
								distributionOrder2.setOrderId(orderInfo.getId());
								distributionOrder2.setShopId(orderInfo.getShopId());
								distributionOrder2.setCommissionStatus(MallConstants.DISTRIBUTION_COMMISSION_STATUS_1);
								distributionOrder2.setCommission(BigDecimal.ZERO);
								List<DistributionOrderItem> listDistributionOrderItem2 = new ArrayList<>();
								listOrderItem.forEach(orderItem2 -> {
									//获取sku信息
									GoodsSku goodsSku2 = goodsSkuService.getById(orderItem2.getSkuId());
									//计算此订单明细的返利金额
									BigDecimal commission2 = BigDecimal.valueOf(goodsSku2.getSecondRebate()).divide(BigDecimal.valueOf(100)).multiply(orderItem2.getPaymentPrice());
									if(commission2.compareTo(BigDecimal.ZERO) == 1) {//返利金额大于0才入库
										DistributionOrderItem distributionOrderItem2 = new DistributionOrderItem();
										distributionOrderItem2.setDistributionOrderId(distributionOrder2.getId());
										distributionOrderItem2.setOrderId(orderItem2.getOrderId());
										distributionOrderItem2.setOrderItemId(orderItem2.getId());
										distributionOrderItem2.setCommission(commission2);
										listDistributionOrderItem2.add(distributionOrderItem2);
										distributionOrder2.setCommission(distributionOrder2.getCommission().add(commission2));
									}
								});
								if(distributionOrder2.getCommission().compareTo(BigDecimal.ZERO) == 1){//返利金额大于0才入库
									//新增分销订单
									distributionOrderService.save(distributionOrder2);
									//批量新增分销订单明细
									listDistributionOrderItem2.forEach(distributionOrderItem2 -> {
										distributionOrderItem2.setDistributionOrderId(distributionOrder2.getId());
									});
									distributionOrderItemService.saveBatch(listDistributionOrderItem2);
									//更新分销员累计佣金金额
									distributionUserService.updateCommissionTotal(distributionUser2.getUserId(),distributionOrder2.getCommission());

									//加入redis，冻结时间自动解冻
									String keyRedis = String.valueOf(StrUtil.format("{}{}:{}",MallConstants.REDIS_DISTRIBUTION_ORDER_THAW, TenantContextHolder.getTenantId(), distributionOrder2.getId()));
									redisTemplate.opsForValue().set(keyRedis, distributionOrder2.getId() , distributionConfig.getFrozenTime() , TimeUnit.HOURS);//设置过期时间
								}
							}
						}
					}
				}
			}

			//发送微信订阅、模板消息
			OrderLogistics orderLogistics = orderLogisticsService.getById(orderInfo.getLogisticsId());
			try {
				WxTemplateMsgSendDTO wxTemplateMsgSendDTO = new WxTemplateMsgSendDTO();
				wxTemplateMsgSendDTO.setMallUserId(orderInfo.getUserId());
				wxTemplateMsgSendDTO.setUseType(ConfigConstant.WX_TMP_USE_TYPE_2);
				wxTemplateMsgSendDTO.setPage("pages/order/order-detail/index?id="+orderInfo.getId());
				Map<String, Object> data = new HashMap();
				Map<String, Object> map = BeanUtil.beanToMap(orderInfo);
				for(String mapKey: map.keySet()){
					data.put("order." + mapKey, map.get(mapKey));
				}
				if(orderLogistics != null){
					Map<String, Object> map2 = BeanUtil.beanToMap(orderLogistics);
					for(String mapKey: map2.keySet()){
						data.put("orderLogistics." + mapKey, map2.get(mapKey));
					}
				}
				wxTemplateMsgSendDTO.setData(data);
				feignWxTemplateMsgService.sendTemplateMsgMa(wxTemplateMsgSendDTO, SecurityConstants.FROM_IN);
				feignWxTemplateMsgService.sendTemplateMsgMp(wxTemplateMsgSendDTO, SecurityConstants.FROM_IN);
			}catch (Exception e){
				log.error("发送微信订阅、模板消息出错："+e.getMessage(), e);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void notifyLogisticsr(String logisticsId, JSONObject jsonObject) {
		OrderLogistics orderLogistics = orderLogisticsService.getById(logisticsId);
		if(orderLogistics != null){
			String status = jsonObject.getStr("status");
			if("abort".equals(status)){//中止
				orderLogistics.setStatus(OrderLogisticsEnum.STATUS_ER.getValue());
				orderLogistics.setMessage(jsonObject.getStr("message"));
			}else{
				orderLogisticsDetailService.remove(Wrappers.<OrderLogisticsDetail>lambdaQuery()
						.eq(OrderLogisticsDetail::getLogisticsId,logisticsId));//先删除
				JSONObject jsonResult =(JSONObject) jsonObject.get("lastResult");
				orderLogistics.setStatus(jsonResult.getStr("state"));
				orderLogistics.setIsCheck(jsonResult.getStr("ischeck"));
				JSONArray jSONArray = jsonResult.getJSONArray("data");
				List<OrderLogisticsDetail> listOrderLogisticsDetail = new ArrayList<>();
				jSONArray.forEach(object -> {
					JSONObject jsonData = JSONUtil.parseObj(object);
					OrderLogisticsDetail orderLogisticsDetail = new OrderLogisticsDetail();
					orderLogisticsDetail.setLogisticsId(logisticsId);
					orderLogisticsDetail.setLogisticsTime(LocalDateTimeUtils.parse(jsonData.getStr("time")));
					orderLogisticsDetail.setLogisticsInformation(jsonData.getStr("context"));
					listOrderLogisticsDetail.add(orderLogisticsDetail);
				});
				orderLogisticsDetailService.saveBatch(listOrderLogisticsDetail);
				//获取最近一条物流信息
				Optional<OrderLogisticsDetail> orderLogisticsDetail = listOrderLogisticsDetail.stream().collect(Collectors.maxBy(Comparator.comparing(OrderLogisticsDetail::getLogisticsTime)));
				orderLogistics.setMessage(orderLogisticsDetail.get().getLogisticsInformation());
			}
			orderLogisticsService.updateById(orderLogistics);
		}
	}

	@Override
	public List<GoodsSku> listSkuInOrderSub(List<PlaceOrderSkuDTO> skus) {
        return goodsSkuMapper.listSkuInOrderSub(skus);
	}

	@Override
	public CouponUser getCouponUserByCouponUserId(String couponUserId) {
		return couponUserService.getById(couponUserId);
	}

	@Override
	public List<GoodsSkuSpecValue> getListGoodsSkuSpecValueBySkuId(String goodsSkuId) {
		return goodsSkuSpecValueMapper.listGoodsSkuSpecValueBySkuId(goodsSkuId);
	}

	@Override
	public boolean updateBatchCouponUserByListCouponUser(List<CouponUser> listCouponUser) {
		return couponUserService.updateBatchById(listCouponUser);
	}


	/**
	 * 校验参数（数量、金额）合法性
	 * @param orderInfo
	 * @param orderItem
	 * @param goodsSku
	 */
	public void verifyOrderItem(OrderInfo orderInfo, OrderItem orderItem, GoodsSku goodsSku){
		//校验参数（数量、金额）合法性
		if(orderItem.getQuantity() < 1){
			throw new RuntimeException("参数（orderItem.quantity）错误："+orderItem.getQuantity());
		}
		if(orderItem.getPaymentPrice().compareTo(BigDecimal.ZERO) ==-1){
			throw new RuntimeException("参数（orderItem.paymentPrice）错误，支付价格不能小于0："+orderItem.getPaymentPrice());
		}
		//计算实际商品单价：(支付金额+积分抵扣金额+电子券抵扣金额-运费金额)/数量
		BigDecimal unitPrice = orderItem.getPaymentPrice()
				.add(orderItem.getPaymentCouponPrice() != null ? orderItem.getPaymentCouponPrice() : BigDecimal.ZERO)
				.add(orderItem.getPaymentPointsPrice() != null ? orderItem.getPaymentPointsPrice() : BigDecimal.ZERO)
				.subtract(orderItem.getFreightPrice() != null ? orderItem.getFreightPrice() : BigDecimal.ZERO);
		unitPrice = unitPrice.divide(BigDecimal.valueOf(orderItem.getQuantity()));
		//普通订单
		if(StrUtil.isBlank(orderInfo.getOrderType()) || MallConstants.ORDER_TYPE_0.equals(orderInfo.getOrderType())){
			//比较计算单价和实际单价是否相等
			if(goodsSku.getSalesPrice().compareTo(unitPrice) != 0){
				log.error("商品单价:" + goodsSku.getSalesPrice());
				log.error("实际商品单价:" + unitPrice);
				throw new RuntimeException("参数（orderItem.paymentPrice）错误，计算单价和实际单价不相等");
			}
		}
		//秒杀订单
		if(MallConstants.ORDER_TYPE_3.equals(orderInfo.getOrderType())){
			SeckillInfo seckillInfo = seckillInfoService.getById(orderInfo.getMarketId());
			//校验金额
			if(seckillInfo.getSeckillPrice().compareTo(unitPrice) != 0){
				throw new RuntimeException("参数（orderItem.paymentPrice）错误，秒杀订单金额不正确");
			}
		}
		//砍价订单
		if(MallConstants.ORDER_TYPE_1.equals(orderInfo.getOrderType())){
			BargainUser bargainUser = bargainUserMapper.selectById(orderInfo.getRelationId());
			BigDecimal bayPrice;//砍价购买价格
			if(CommonConstants.YES.equals(bargainUser.getFloorBuy())){
				//必须底价购买（1：是），砍价购买价格 = 砍价底价
				bayPrice = bargainUser.getBargainPrice();
			}else{
				//必须底价购买（0：否），砍价购买价格 = 商品价格-已砍金额
				BigDecimal totalCutPrice = bargainCutService.getTotalCutPrice(orderInfo.getRelationId());
				bayPrice = goodsSku.getSalesPrice().subtract(totalCutPrice);
			}
			//校验金额
			if(bayPrice.compareTo(unitPrice) != 0){
				throw new RuntimeException("参数（orderItem.paymentPrice）错误，砍价订单金额不正确");
			}
		}
		//拼团订单
		if(MallConstants.ORDER_TYPE_2.equals(orderInfo.getOrderType())){
			GrouponInfo grouponInfo = grouponInfoMapper.selectById(orderInfo.getMarketId());
			//校验金额
			if(grouponInfo.getGrouponPrice().compareTo(unitPrice) != 0){
				throw new RuntimeException("参数（orderItem.paymentPrice）错误，拼团订单金额不正确");
			}
		}
	}
}
