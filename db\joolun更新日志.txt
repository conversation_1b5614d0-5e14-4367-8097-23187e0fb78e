﻿一. 1.0.0升级1.0.1
1. 商城用户端支持编译成app（安卓、ios）
1. 新增商城秒杀功能
1. 新增店铺商品分类
1. 完善商城店铺首页，增加tabbar（首页、全部商品、分类、客服）
1. spring-cloud升级到Hoxton.SR8版本
1. spring-cloud-alibaba升级到2.2.3.RELEASE版本
1. dynamic-datasource-spring-boot-starter升级到3.2.0版本
1. spring-boot升级到2.3.4.RELEASE版本
1. weixinJava升级到3.9.0版本
1. avue升级到2.6.16版本
1. hutool升级到5.4.2版本
1. 小程序直播组件升级到1.1.9版本
1. 修复搜索历史无法删除的问题https://git.joolun.com/joolun-multi/joolun-plus/issues/3
1. 修复后台优惠券详情查看,不能复现已选择商品列表信息https://git.joolun.com/joolun-multi/joolun-plus/issues/4
1. 修复h5生成海报时的跨域问题
1. 修复后台素材库样式问题
1. 修复优惠券不可用时还能点击的bug
1. 修复勾选积分抵扣时，积分不能正常抵扣的bug
1. 修复部分手机订单列表页面不显示tab标签的bug
1. 修复商户端app修改密码少传了两个字段，导致修改失败的bug
1. 由于商城app端tabbar不支持网络图标，tabbar的图标改成只能选择本地已经有图标

一. 1.0.1升级1.0.2
1. 新增店铺入驻功能
1. 增加【店员管理】功能
1. 实现商城首页动态拖拽装修（支持不同用户端装修不同的效果）
1. 新增XXL-JOB定时任务功能
1. 增加“定时清理未正常自动取消的订单”的机制
1. seata升级到1.3.0版本
1. avue升级到2.6.18版本
1. 商城登录成功后重定向到进入时的页面 https://git.joolun.com/joolun-multi/joolun-plus/issues/46
1. 砍价商品分享出去链接错误 https://git.joolun.com/joolun-multi/joolun-plus/issues/49
1. 首页导航设置跳转到商品分类无效 https://git.joolun.com/joolun-multi/joolun-plus/issues/42
1. h5生成海报的问题 https://git.joolun.com/joolun-multi/joolun-plus/issues/48
1. 更新店铺分类出现问题 https://git.joolun.com/joolun-multi/joolun-plus/issues/34
1. 修复编辑商品信息选择运费模板时，店铺商品类目会被重置的问题 https://git.joolun.com/joolun-multi/joolun-plus/issues/45
1. 新增商品分类 选择跳转页面报错，未选择跳转页面也可以成功保存 https://git.joolun.com/joolun-multi/joolun-plus/issues/29
1. app端增加生成海报功能
1. app端分享和海报url新增公众号appId参数

一. 1.0.2升级1.0.3
1. 商城店铺首页实现拖拽自定义装修
1. 商城首页和店铺首页装修增加4大营销组件
1. 增加“实时资讯”文章功能
1. 增加“用户足迹”功能
1. 后台登录页面增加“商家入驻”入口
1. 后台增加“缓存监控”功能
1. 后台增加“商品收藏”、“店铺收藏”页面
1. 后台增加小程序直播管理
1. 商城、店铺商品分类增加是否显示字段控制是否展示
1. 小程序直播组件升级到1.2.5版本
1. wxJava升级到4.0.0版本
1. 拒绝退款后支持再次申请退款
1. 修复支付时报appid和openid不一致的问题
1. 修复已经完成拒绝退款，订单明细表的状态还是“退款中”的问题
1. 修复小程序登录时有时会触发“uk_appid_openid"统一约束的问题
1. 修复分页条件查询，如果没有数据的话，页面显示的是暂无数据，但是分页那里显示的还是共多少条，可以点击页数的问题
1. 修复小程序请求有时会出现 Expected BEGIN_OBJECT but was STRING at line 1 column 1 path $的问题(#62) 
1. 修复分页信息显示不正确的问题(#79) 
1. 修复商户端的退款详情页面的退款状态判断错误(#66) 
1. 修复优惠券领取后，提交订单页面价格计算错误的问题(#67) 
1. 修复主题设置中选中的底部tabbar状态无法回显(#82) 
1. 修复首页装修bug,顶部操作栏显示异常(#86) 
1. 修复富文本不居中问题(#68) 
1. 修复店铺入住编辑店铺头像和用户头像回显不出来(#84) 
1. 修复店员管理菜单点击登录日志异常(#80) 
1. 优化首页装修-小程序-商品甄选组件-选择商品时，跨页则会把已选择的商品项清零(#70) 

一. 1.0.3升级1.0.4
1. 公众号h5授权支持第三方平台接入(#93)
1. 商城增加”用户积分签到”功能
1. nacos升级到1.4.1版本
1. spring-cloud-alibaba升级到2.2.4.RELEASE版本
1. Sentinel代替Hystrix
1. 增加joolun-monitor服务（springboot admin监控中心）
1. order_info、user_info的app_type字段重新定义成：来源应用（MA：小程序；H5：普通H5；H5-WX：微信H5；APP：app）

一. 1.0.4升级1.0.5
1. 增加小程序第三方平台一键授权添加
1. 增加小程序第三方平台代码管理发布功能
1. 增加小程序第三方平台服务器域名管理功能
1. 后台增加小程序直播商品管理
1. 后台装修商品分类组件数据改成动态加载
1. 装修商品分类组件数据过滤“未显示”数据

一. 1.0.5升级1.0.6
1. 增加“店铺用户”
1. 后台增加店铺公众号H5二维码和小程序码
1. 后台商品评价、砍价管理、拼团管理、秒杀管理增加商品过滤条件
1. 后台用户管理增加“消费记录”、“领券记录”查看
1. 商城订单支付成功后跳转至“支付结果页面”
1. 商城首页增加下拉刷新功能
1. 商品增加审核功能，增加或修改商品后需审核通过了才能上架到商城
1. quill编辑器支持秀米和135样式
1. 小程序端修复分享店铺海报小程序码报找不到店铺的问题
1. 修复小程序端商品详情页面规格无效时还能选择的问题
1. 后台修复商品评价店铺条件过滤无效的问题
1. 修复web商城装修时添加【商品甄选】组件时后台没有显示出相应的组件框的问题#113
1. 修复首页报错#115

一. 1.0.6升级1.0.7
1. 新增商城分销功能
1. 新增【用户消费记录】功能，记录用户总计订单数、总计消费金额
1. 新增商城端用户个人信息修改功能
1. 新增商城端用户手机号修改功能
1. 登录日志增加登录失败状态的记录
1. 商品评价增加时间过滤条件
1. avue升级到2.8.4版本
1. 特约商户进件增加结算银行账户bank_account_info
1. 优化数据权限使用跨服务调用获取角色
1. 修复秒杀分享无法生成海报的问题 https://git.joolun.com/joolun-multi/joolun-plus/commit/0380e221d56db660482f7cfe9c219436a0f60a23
1. 小程序获取用户信息接口调整wx.getUserProfile
1. 修复保障服务删除后商品里的基础服务显示空 (#141)
1. 修复app端分享时，如果appId是通过一键授权添加的，会报错的问题
1. 修复后台解锁后每次刷新都需要输入锁屏密码的问题
1. 修复后台店铺装修素材库shopId传错的问题 (#144)
1. 修复小程序分享不同店铺的海报，扫出来的是同一个店铺的问题 (#152)

一. 1.0.7升级1.0.8
1. 商城端商品评价增加评价图片上传
1. spring-boot升级到2.3.11.RELEASE版本
1. spring-cloud-alibaba升级至2.2.5.RELEASE版本
1. nacos升级到1.4.2版本
1. 优化数据权限使用跨服务调用获取角色
1. 后台订单列表增加订单类型筛选条件
1. 更改分享海报二维码大小，提高识别率
1. 修复APP打包编译时的报错警告
1. 修复后台table操作按钮不显示的问题
1. 店铺下架后相应商品页面提示“店铺下架”的信息，限制商品的购买
1. 店铺下架后购物车内的相应商品变为无效状态
1. 店铺下架后相应订单限制调起支付

一. 1.0.8升级1.0.9
1. 商城PC端正式上线
1. 新增joolun-common-nacos模块用于操作nacos配置文件
1. 修复application-dev.yml配置文件修改不自动刷新的问题
1. 后台新增极光账号、第三方登录、商城配置、短信配置、邮箱配置、微信第三方平台配置的可视化设置
1. element-ui升级到2.15.3版本
1. 下单接口增加商品数量和金额的合法性校验(#173)
1. 商城用户增加乐观锁机制防止重复返积分(#173)
1. 优化后台的修改商城用户接口(#173)
1. 在删除商品的时候加上判断，如该商品有参与营销活动就不能删除 (#175)
1. 修复后台表格x轴无法滚动的问题

一. 1.0.9升级1.0.10
1. PC端商城首页、店铺首页自定义装修
1. nacos升级到2.0.3版本
1. spring-boot升级到2.5.2版本
1. spring-cloud升级到2020.0.3版本
1. spring-cloud-alibaba升级到2021.1版本
1. mybatis-plus升级到3.4.3版本
1. dynamic-datasource-spring-boot-starter升级到3.4.1版本
1. druid升级到1.2.6版本
1. hutool升级到5.7.3
1. weixinJava升级到4.1.0版本
1. element-ui升级到2.15.3版本
1. 增加feign调用统一fallback处理
1. 增加数据脱敏工具
1. 商城端已登录用户增加密码修改入口
1. 后台修复修改店员密码无法登录的问题(#200)
1. 商户端APP修复签收信息错误的显示为订单留言的问题
1. 商户端APP修复订单总费用显示错误的问题
1. 商户端APP修复退出登录重新登录后app极光初始化失败的问题
1. 修复删除微信小程序的，未删除微信用户导致再次接入后无法登录的问题
### 升级此版本前记得一定要清redis缓存，本次升级没有增量sql

一. 1.0.10升级1.0.11
1. 新增支付宝服务商收款功能（支持：手机APP、手机H5、电脑H5）
1. 后台店铺新增支付宝第三方应用授权接入
1. 移动端商城首页增加骨架屏
1. 移动端增加单独支付页面
1. avue升级到2.8.22版本
1. 优化微信用户登录商城的逻辑：同一unionId的小程序和公众号用户，当有一端绑定过商城用户，另一端进入商城时不再需要再次绑定
1. 退款回调接口从joolun-mall-api移至joolun-mall-admin
1. 优化后台token过期时的提示信息
1. 优化后台微信支付商户进件功能
1. 公众号群发选择用户逻辑改成累加
1. 公众号回复消息音乐类型增加上传功能
1. 修复机构编辑时报错的问题(#210)
1. 修复后台"领券记录"报错的问题
1. 修复mybatisPlus size小于0时查所有数据的bug
1. 修复店员管理、分销设置、店铺入驻中素材上传报错的问题
1. 修复由升级mybatisPlus版本引起的店铺数据权限查询不正常的问题
1. 修复joolun-mall-api服务生产环境“java.lang.IllegalStateException: UT010005: Cannot call getOutputStream(), getWriter() already called”的报错问题
1. 修复商城端用户登录成功有时不跳转页面的问题
1. 修复0元退款不成功的问题
1. 修复后台公众号自定义菜单选择跳转页面不正常的问题
1. 修复商城移动端拼团详情页商品信息不显示的问题
### 升级此版本前记得一定要清redis缓存

一. 1.0.11升级1.0.12
1. 后台系统用户增加【系统管理员】类型，可通过切换租户来管理其他租户的数据；默认admin账号为【系统管理员】类型；此次升级后台各菜单位置有所调整
1. 新增微信公众号模板消息并能发送订单状态给用户微信
1. 优化微信订阅、模板消息的配置和发送逻辑
1. 商城端增加我的评价模块
1. 新增评价追评功能
1. 商户app增加店铺用户管理及用户消费明细模块
1. 租户新增logo、官方网址属性
1. 升级echart版本为5.2.1
1. 新增PC端商城装修logo及商城名称修改
1. 新增PC端商城装修页脚自定义
1. 物流公司实现可配置化，从OrderLogisticsEnum类移到nacos中；order_logistics增加logistics_desc字段记录物流商家名
1. 修复新租户无法装修PC首页问题
### 升级此版本一定要清redis缓存，[严格按照要求做](https://git.joolun.com/joolun-multi/joolun-plus/wiki/%E4%B8%80-%E7%89%88%E6%9C%AC%E5%8D%87%E7%BA%A7%E8%AF%B4%E6%98%8E)


一. 1.0.12升级1.0.13
1. 将系统管理员单独移出租户，可在后台【平台管理】-【平台系统配置】单独维护
1. 可给不同平台系统用户指定不同的租户
1. 增加配置小程序用户隐私保护指引
1. 商城端用户登录时增加通过IP获取保存用户的省份和市区功能
1. 修复订单导出多个商品商品名不全的问题
1. 修复订单金额出现负数的问题
1. 修复后台装修单图组件的跳转链接回显bug
1. 修复订单商品优惠券使用时可能为负数的bug 
1. 修复小程序 webview url不显示问题
1. 修复聊天页ios刘海屏，输入框位置和键盘中间间隙显示问题(#228)
1. 修复商户app端，发货时获取快递公司报错的问题
### 升级此版本一定要清redis缓存，[严格按照要求做](https://git.joolun.com/joolun-multi/joolun-plus/wiki/%E4%B8%80-%E7%89%88%E6%9C%AC%E5%8D%87%E7%BA%A7%E8%AF%B4%E6%98%8E)

一. 1.0.13升级1.0.14
1. 后台新增主题装修预览
1. 后台新增公众号模板消息预览
1. weixinJava升级到4.2.0版本
1. 修复升级springBoot后公众号用户消息WebSocket连接报错无法实时的问题(#249)
1. 修复小程序直播新增提示成功，查询又没有的问题
1. 修复后台物流公司无法删除的问题
1. 修复后台右上方消息图标全部消息404的问题
1. 修复后台平台系统管理员无法绑定第三方登录的问题
1. 修复新增移动端装修超过2个后无法新增的问题
1. 复新增店铺上传图片报错的问题
1. 修复自提订单不发订阅消息的问题
1. 修复后台添加菜单后管理员菜单变空的问题

一. 1.0.14升级1.0.16
1. 新增装修个性化组件
1. 微信公众号移除素材库中的图文，升级新增为草稿箱、已发布图文
1. 将微信公众号自定义菜单、用户消息、消息自动回复、消息群发中的图文换成已发布的图文
1. weixinJava升级到4.2.8.B版本
1. spring-cloud-starter-gateway升级到3.0.7版本，加固Spring Cloud Gateway 远程代码执行漏洞
1. 后台订单导出新增收货人信息
1. 优化后台小程序直播校验
1. 优化支付模块服务间调用操作失败后的回返信息
1. 修复后台商品多规格SKU是否启用按钮不回显的问题
1. 修复商城苹果Safari浏览器长按无法保存图片的问题
1. 修复iOS系统中浏览器h5调用支付宝失败问题
1. 修复商城首页下拉刷新数据不更新的问题
1. 修复在平台素材库里上传图片时使用当前租户存储配置的问题
1. 修复后台小程序直播无法修改的问题
1. 修复后台登录日志租户字段入值不正确的问题
1. 修复后台订单操作后不刷新的问题(#306)
1. 修复后台新增商品时分销比例不能为0的问题(#299)