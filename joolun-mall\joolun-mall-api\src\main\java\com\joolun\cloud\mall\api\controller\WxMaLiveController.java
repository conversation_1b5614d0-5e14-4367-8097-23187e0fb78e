/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.util.ApiUtil;
import com.joolun.cloud.mall.common.feign.FeignWxLiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;

/**
 * 小程序直播
 *
 * <AUTHOR>
 * @date 2019-03-23 21:26:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wxmalive")
@Api(value = "wxmalive", tags = "小程序直播API")
public class WxMaLiveController {

	private final FeignWxLiveService feignWxLiveService;

	/**
	* 获取直播房间列表
	* @return
	*/
	@ApiOperation(value = "获取直播房间列表")
	@GetMapping("/roominfo/list")
	public R getWxMaterialList(HttpServletRequest request) {
		return feignWxLiveService.getWxRoominfoList(ApiUtil.getAppId(request), SecurityConstants.FROM_IN);
	}
}
