/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.core.util.SensitiveUtils;
import com.joolun.cloud.common.jiguang.config.JiguangConfigProperties;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.common.nacos.util.NacosConfigUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.yaml.snakeyaml.Yaml;

import java.util.HashMap;
import java.util.Map;

/**
 * 极光配置
 *
 * <AUTHOR>
 * @date 2020-11-24 14:44:15
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/jiguangconfig")
@Api(value = "jiguangConfig", tags = "极光配置管理")
public class JiguangConfigController {

	private NacosConfigUtils nacosConfigUtils;

    /**
     * 极光配置查询
     * @param
     * @return R
     */
    @ApiOperation(value = "极光配置查询")
    @GetMapping
    @PreAuthorize("@ato.hasAuthority('mall:jiguangconfig:get')")
    public R getBy() throws NacosException {
		ConfigService configService = nacosConfigUtils.getConfigService();
		String content = configService.getConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP, CommonConstants.CONFIG_TIMEOUT_MS);
		Yaml yaml = new Yaml();
		Map<String,Object> confMap= yaml.load(content);
		Object object = confMap.get("jiguang");
		JSONObject jSONObject = JSONUtil.parseObj(object);
		JiguangConfigProperties jiguangConfigProperties = jSONObject.toBean(JiguangConfigProperties.class);
		//数据脱敏
		String sensitiveSecret = SensitiveUtils.process(jiguangConfigProperties.getSecret());
		jiguangConfigProperties.setSecret(sensitiveSecret);
		return R.ok(jiguangConfigProperties);
    }

    /**
     * 极光配置修改
     * @param jiguangConfigProperties 极光配置
     * @return R
     */
    @ApiOperation(value = "极光配置修改")
    @SysLog("修改极光配置")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:jiguangconfig:edit')")
    public R update(@RequestBody JiguangConfigProperties jiguangConfigProperties) throws NacosException {
		ConfigService configService = nacosConfigUtils.getConfigService();
		String content = configService.getConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP, CommonConstants.CONFIG_TIMEOUT_MS);
		Yaml yaml = new Yaml();
		Map<String,Map> confMap= yaml.load(content);

		String secret = confMap.get("jiguang").get("secret")+"";
		String sensitiveSecret = SensitiveUtils.process(secret);
		if(!sensitiveSecret.equals(jiguangConfigProperties.getSecret())){
			secret = jiguangConfigProperties.getSecret();
		}

		Map jiguangMap = new HashMap();
		jiguangMap.put("appKey",jiguangConfigProperties.getAppKey());
		jiguangMap.put("secret",secret);
		jiguangMap.put("flag",jiguangConfigProperties.getFlag());
		confMap.put("jiguang",jiguangMap);
		String confStr = yaml.dumpAsMap(confMap);
		boolean rs = configService.publishConfig(CommonConstants.CONFIG_DATA_ID_APPLICATION, CommonConstants.CONFIG_GROUP,confStr,"yaml");
        return R.ok(rs);
    }

}
