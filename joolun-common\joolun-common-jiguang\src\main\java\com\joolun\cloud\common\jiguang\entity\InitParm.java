package com.joolun.cloud.common.jiguang.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class InitParm implements Serializable {
	/**
	 * 由 JPush Web Portal 生成的 24位字符串。字母或者数字，不区分大小写
	 */
	private String appkey;
	/**
	 * 随机字符串
	 */
	private String random_str;
	/**
	 * 当初时间戳
	 */
	private String signature;
	/**
	 * 签名
	 */
	private Long timestamp;
	/**
	 * 是否启用消息记录漫游，默认 0 否，1 是
	 */
	private Integer flag;

}
