/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.UserShop;
import com.joolun.cloud.mall.admin.service.UserShopService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 店铺用户
 *
 * <AUTHOR>
 * @date 2021-03-29 14:26:20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/usershop")
@Api(value = "usershop", tags = "店铺用户管理")
public class UserShopController {

    private final UserShopService userShopService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param userShop 店铺用户
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:usershop:index')")
    public R getPage(Page page, UserShop userShop) {
        return R.ok(userShopService.page1(page, userShop));
    }

    /**
     * 店铺用户查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺用户查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:usershop:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(userShopService.getById(id));
    }

    /**
     * 店铺用户新增
     * @param userShop 店铺用户
     * @return R
     */
    @ApiOperation(value = "店铺用户新增")
    @SysLog("新增店铺用户")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:usershop:add')")
    public R save(@RequestBody UserShop userShop) {
        return R.ok(userShopService.save(userShop));
    }

    /**
     * 店铺用户修改
     * @param userShop 店铺用户
     * @return R
     */
    @ApiOperation(value = "店铺用户修改")
    @SysLog("修改店铺用户")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:usershop:edit')")
    public R updateById(@RequestBody UserShop userShop) {
        return R.ok(userShopService.updateById(userShop));
    }

    /**
     * 店铺用户删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "店铺用户删除")
    @SysLog("删除店铺用户")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:usershop:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(userShopService.removeById(id));
    }

	/**
	 * 查询数量
	 * @param userShop
	 * @return
	 */
	@ApiOperation(value = "查询数量")
	@GetMapping("/count")
	public R getCount(UserShop userShop) {
		return R.ok(userShopService.count(Wrappers.query(userShop)));
	}

	/**
	 * 查询统计
	 * @param userShop
	 * @return
	 */
	@ApiOperation(value = "查询统计")
	@GetMapping("/statistics")
	public R getStatistics(UserShop userShop) {
		//总数量
		int countTotal = userShopService.count(Wrappers.query(userShop));
		//今天数量
		LocalDate localDate = LocalDateTime.now().toLocalDate();
		int countToday = userShopService.count(Wrappers.query(userShop).between("create_time", LocalDateTime.of(localDate, LocalTime.MIN), LocalDateTime.of(localDate, LocalTime.MAX)));
		Map<String, Object> rs = new HashMap<>();
		rs.put("countTotal",countTotal);
		rs.put("countToday",countToday);
		return R.ok(rs);
	}
}
