<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.GoodsSpuSpecMapper">

	<resultMap id="goodsSpuSpecMap" type="com.joolun.cloud.mall.common.entity.GoodsSpuSpec">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="spuId" column="spu_id"/>
		<result property="specId" column="spec_id"/>
		<result property="specName" column="spec_name"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="sort" column="sort"/>
	</resultMap>

	<resultMap id="goodsSpecVoMap" type="com.joolun.cloud.mall.common.vo.GoodsSpecVO">
		<id property="id" column="spec_id"/>
		<id property="spuId" column="spu_id"/>
		<result property="value" column="spec_name"/>
		<collection property="leaf"
					select="com.joolun.cloud.mall.admin.mapper.GoodsSkuSpecValueMapper.selectBySpecId"
					column="{specId=spec_id,spuId=spu_id}" >
		</collection>
	</resultMap>

	<select id="selectTree" resultMap="goodsSpecVoMap">
		SELECT
		  gss.`spu_id`,
		  gss.`spec_id`,
		  gs.`name` AS spec_name
		FROM
		  goods_spu_spec gss
		  LEFT JOIN `goods_spec` gs
			ON gs.`id` = gss.`spec_id`
		WHERE gss.`spu_id` = #{spuId}
		ORDER BY gss.`sort` ASC
	</select>
</mapper>
