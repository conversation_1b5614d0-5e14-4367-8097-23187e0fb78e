/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.service.SeckillHallInfoService;
import com.joolun.cloud.mall.api.service.SeckillHallService;
import com.joolun.cloud.mall.api.service.SeckillInfoService;
import com.joolun.cloud.mall.common.constant.MyReturnCode;
import com.joolun.cloud.mall.common.entity.BargainUser;
import com.joolun.cloud.mall.common.entity.SeckillHall;
import com.joolun.cloud.mall.common.entity.SeckillHallInfo;
import com.joolun.cloud.mall.common.entity.SeckillInfo;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;

/**
 * 秒杀商品
 *
 * <AUTHOR>
 * @date 2020-08-12 16:16:45
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/seckillinfo")
@Api(value = "seckillinfo", tags = "秒杀商品API")
public class SeckillInfoController {

    private final SeckillInfoService seckillInfoService;

	private final SeckillHallInfoService seckillHallInfoService;

	private final SeckillHallService seckillHallService;

	/**
	 * 分页列表
	 * @param page 分页对象
	 * @param seckillInfo
	 * @return
	 */
	@ApiOperation(value = "分页列表")
	@GetMapping("/page")
	public R getPage(Page page, SeckillInfo seckillInfo, SeckillHallInfo seckillHallInfo) {
		seckillInfo.setEnable(CommonConstants.YES);
		return R.ok(seckillInfoService.page2(page, seckillInfo, seckillHallInfo));
	}

	/**
	 * 秒杀商品查询
	 * @param seckillHallInfoId
	 * @return R
	 */
	@ApiOperation(value = "秒杀商品查询")
	@GetMapping("/{seckillHallInfoId}")
	public R getById(@PathVariable("seckillHallInfoId") String seckillHallInfoId) {
		SeckillHallInfo seckillHallInfo = seckillHallInfoService.getById(seckillHallInfoId);
		if(seckillHallInfo == null){
			return R.failed(MyReturnCode.ERR_80046.getCode(), MyReturnCode.ERR_80046.getMsg());
		}
		//查询秒杀商品
		SeckillInfo seckillInfo = seckillInfoService.getById2(seckillHallInfo.getSeckillInfoId());
		//查询会场信息
		SeckillHall seckillHall  = seckillHallService.getById(seckillHallInfo.getSeckillHallId());
		seckillInfo.setSeckillHall(seckillHall);
		return R.ok(seckillInfo);
	}
}
