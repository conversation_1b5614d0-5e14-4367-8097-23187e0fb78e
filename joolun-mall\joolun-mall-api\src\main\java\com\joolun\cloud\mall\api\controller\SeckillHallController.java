/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.service.SeckillHallService;
import com.joolun.cloud.mall.common.entity.SeckillHall;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;

/**
 * 秒杀会场
 *
 * <AUTHOR>
 * @date 2020-08-12 16:12:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/seckillhall")
@Api(value = "seckillhall", tags = "秒杀会场API")
public class SeckillHallController {

    private final SeckillHallService seckillHallService;

	/**
	 * list列表
	 * @param seckillHall
	 * @return
	 */
	@ApiOperation(value = "list列表")
	@GetMapping("/list")
	public R getList(SeckillHall seckillHall) {
		seckillHall.setEnable(CommonConstants.YES);
		return R.ok(seckillHallService.list(Wrappers.query(seckillHall).orderByAsc("hall_time")));
	}
}
