package com.joolun.cloud.weixin.api.controller;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.weixin.common.constant.MyReturnCode;
import com.joolun.cloud.weixin.api.service.AuctionCalendarService;
import com.joolun.cloud.weixin.api.service.AppointRulesService;
import com.joolun.cloud.weixin.common.dto.AuctionCalendarDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 档期时间表
 *
 * <AUTHOR>
 * @date 2022-02-19 14:33:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/auctioncalendar")
@Api(value = "auctioncalendar", tags = "档期时间表管理")
public class AuctionCalendarController {

	private final AuctionCalendarService auctionCalendarService;
	private final AppointRulesService appointRulesService;

	/**
	 * 得到当天档期
	 *
	 * @return
	 */
	@ApiOperation(value = "得到当天档期")
	@GetMapping("/oneday")
	public R getListByCon(AuctionCalendarDTO auctionCalendarDTO) {
		return R.ok(auctionCalendarService.getListByCon(auctionCalendarDTO));
	}

	/**
	 * 得到档期信息
	 *
	 * @return
	 */
	@ApiOperation(value = "得到档期信息")
	@GetMapping("/info")
	public R getAppointInfo(AuctionCalendarDTO auctionCalendarDTO) {
		return R.ok(auctionCalendarService.getAppointInfo(auctionCalendarDTO));
	}



	/**
	 * 档期时间表查询
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "档期时间表查询")
	@GetMapping("/{id}")
	public R getById(@PathVariable("id") String id) {
		return R.ok(auctionCalendarService.getById(id));
	}

	/**
	 * 档期时间表新增
	 * @param auctionCalendarDTO 档期时间表
	 * @return R
	 */
	@ApiOperation(value = "档期时间表新增")
	@PostMapping
	public R save(@RequestBody AuctionCalendarDTO auctionCalendarDTO) {
		if (ObjectUtils.isNull(auctionCalendarDTO.getStartTime()) || ObjectUtils.isNull(auctionCalendarDTO.getEndTime())) {
			return R.failed(MyReturnCode.ERR_70000.getCode(), MyReturnCode.ERR_70000.getMsg());
		}
		return auctionCalendarService.saveAppoint(auctionCalendarDTO);
	}


	/**
	 * 重选档期
	 * @param auctionCalendarDTO 档期时间表
	 * @return R
	 */
	@ApiOperation(value = "重选档期")
	@PostMapping("reset")
	public R resetAppoint(@RequestBody AuctionCalendarDTO auctionCalendarDTO) {
		if (ObjectUtils.isNull(auctionCalendarDTO.getStartTime()) || ObjectUtils.isNull(auctionCalendarDTO.getEndTime())) {
			return R.failed(MyReturnCode.ERR_70000.getCode(), MyReturnCode.ERR_70000.getMsg());
		}
		return auctionCalendarService.resetAppoint(auctionCalendarDTO);
	}

	/**
	 * 根据支付页面ID获取页面ID
	 * @param payPageId 支付页面ID
	 * @return R
	 */
	@ApiOperation(value = "根据支付页面ID获取页面ID")
	@GetMapping("/getPageIdByPayPageId")
	public R getPageIdByPayPageId(@RequestParam String payPageId) {
		if (ObjectUtils.isEmpty(payPageId)) {
			return R.failed("支付页面ID不能为空");
		}
		String pageId = appointRulesService.getPageIdByPayPageId(payPageId);
		if (ObjectUtils.isEmpty(pageId)) {
			return R.failed("未找到对应的页面ID");
		}
		return R.ok(pageId);
	}

	/**
	 * 取消预约档期
	 * @param auctionCalendarDTO 档期时间表
	 * @return R
	 */
	@ApiOperation(value = "取消预约档期")
	@PostMapping("cancel")
	public R cancelAppoint(@RequestBody AuctionCalendarDTO auctionCalendarDTO) {
		if (ObjectUtils.isEmpty(auctionCalendarDTO.getPageId()) || ObjectUtils.isEmpty(auctionCalendarDTO.getAppointId())) {
			return R.failed("页面ID和档期ID不能为空");
		}
		return auctionCalendarService.cancelAppoint(auctionCalendarDTO);
	}

}