<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.weixin.admin.mapper.OrderInfoMapper">

	<resultMap id="orderInfoMap" type="com.joolun.cloud.weixin.common.entity.OrderInfo">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="shopId" column="shop_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="appType" column="app_type"/>
		<result property="appId" column="app_id"/>
		<result property="userId" column="user_id"/>
		<result property="orderNo" column="order_no"/>
		<result property="salesPrice" column="sales_price"/>
		<result property="tradeType" column="trade_type"/>
		<result property="paymentPrice" column="payment_price"/>
		<result property="paymentTime" column="payment_time"/>
		<result property="transactionId" column="transaction_id"/>
		<result property="remark" column="remark"/>
		<result property="isPay" column="is_pay"/>
		<result property="name" column="name"/>
		<result property="status" column="status"/>
		<result property="orderType" column="order_type"/>
		<result property="marketId" column="market_id"/>
		<result property="pageId" column="page_id"/>
		<result property="relationId" column="relation_id"/>
	</resultMap>


	<sql id="orderInfoSql">
		order_info.`id`,
		order_info.`tenant_id`,
		order_info.`shop_id`,
		order_info.`del_flag`,
		order_info.`create_time`,
		order_info.`update_time`,
		order_info.`app_type`,
		order_info.`app_id`,
		order_info.`user_id`,
		order_info.`order_no`,
		order_info.`trade_type`,
		order_info.`is_pay`,
		order_info.`name`,
		order_info.`status`,
		order_info.`sales_price`,
		order_info.`payment_time`,
		order_info.`transaction_id`,
		order_info.`order_type`,
		order_info.`market_id`,
		order_info.`relation_id`,
		order_info.`page_id`,
		order_info.`remark`
	</sql>

	<update id="bathUpdateOrderStatus">
		UPDATE  order_info oi
-- 		SET oi.status = 3
		SET oi.status = 11
		WHERE
		oi.del_flag = 0
		<if test="list != null and list.size() > 0">
			AND oi.id IN
			<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
				#{item.id}
			</foreach>
		</if>
	</update>

	<select id="getPage" resultType="com.joolun.cloud.weixin.common.vo.OrderInfoVO">
		SELECT
		o.*,p.page_name,u.nick_name,u.phone,o.remark as orderPhone
		FROM
		order_info o
		INNER JOIN  wx_page_devise p on  o.page_id = p.id
		INNER JOIN  wx_user u on  o.user_id = u.id
		<where>
			<if test="query.shopId != null ">
				o.`shop_id` = #{query.shopId}
			</if>
			<if test="query.pageId != null and query.pageId != '' ">
				And o.page_id = #{query.pageId}
			</if>
			<if test="query.isPay != null and query.isPay != '' ">
				And o.is_pay = #{query.isPay}
			</if>
			<if test="query.phone != null and query.phone != '' ">
				And (u.phone LIKE CONCAT('%',#{query.phone},'%') OR o.remark LIKE CONCAT('%',#{query.phone},'%'))
			</if>
			<if test="query.paymentPrice != null ">
				And o.payment_price = #{query.paymentPrice}
			</if>
			<if test="query.appIdList != null and query.appIdList.size()>0">
				<foreach item="item" index="index" collection="query.appIdList" open="AND(" separator="or" close=")">
					o.app_id = #{item}
				</foreach>
			</if>
			<if test="query.pageTypeList != null and query.pageTypeList.size()>0">
				<foreach item="item" index="index" collection="query.pageTypeList" open="AND(" separator="or" close=")">
					p.page_type = #{item}
				</foreach>
			</if>
			<if test="query.orderTypeList != null and query.orderTypeList.size()>0">
				<foreach item="item" index="index" collection="query.orderTypeList" open="AND(" separator="or" close=")">
					o.order_type = #{item}
				</foreach>
			</if>
		</where>
		ORDER BY o.`payment_time` DESC;
	</select>
</mapper>
