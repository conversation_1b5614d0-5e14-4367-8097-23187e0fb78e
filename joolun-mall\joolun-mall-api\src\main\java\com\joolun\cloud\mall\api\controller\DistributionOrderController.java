/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.service.DistributionOrderService;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import com.joolun.cloud.mall.common.entity.DistributionOrder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 分销订单
 *
 * <AUTHOR>
 * @date 2021-04-30 10:26:30
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/distributionorder")
@Api(value = "distributionorder", tags = "分销订单API")
public class DistributionOrderController {

    private final DistributionOrderService distributionOrderService;

    /**
     * 分销订单分页列表
     * @param page 分页对象
     * @param distributionOrder 分销订单
     * @return
     */
    @ApiOperation(value = "分销订单分页列表")
    @GetMapping("/page")
	@ApiLogin
    public R getPage(Page page, DistributionOrder distributionOrder) {
    	//查询当前用户为分销员的订单
		distributionOrder.setDistributionUserId(ThirdSessionHolder.getUserId());
        return R.ok(distributionOrderService.page1(page, distributionOrder));
    }

	/**
	 * 获取当前分销员的冻结金额
	 * @return
	 */
	@ApiOperation(value = "获取当前分销员的冻结金额")
	@GetMapping("/frozenAmount")
	@ApiLogin
	public R getDistributionUserFrozenAmount() {
		return R.ok(distributionOrderService.getDistributionUserFrozenAmount(ThirdSessionHolder.getUserId()));
	}
}
