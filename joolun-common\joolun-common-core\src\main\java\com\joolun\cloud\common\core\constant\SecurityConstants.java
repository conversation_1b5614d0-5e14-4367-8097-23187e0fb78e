package com.joolun.cloud.common.core.constant;

/**
 * <AUTHOR>
public interface SecurityConstants {
	/**
	 * 密码登录
	 */
	String PASSWORD = "password";
	/**
	 * 短信登录验证码
	 */
	String SMS_LOGIN = "sms_login";
	/**
	 * 刷新
	 */
	String REFRESH_TOKEN = "refresh_token";
	/**
	 * 验证码有效期（5分钟）
	 */
	int CODE_TIME = 300;
	/**
	 * 验证码长度
	 */
	String CODE_SIZE = "4";
	/**
	 * 角色前缀
	 */
	String ROLE = "ROLE_";
	/**
	 * 前缀
	 */
	String BASE_PREFIX = "base_";

	/**
	 * oauth 相关前缀
	 */
	String OAUTH_PREFIX = "oauth:";
	/**
	 * 项目的license
	 */
	String BASE_LICENSE = "Copyright © JooLun";

	/**
	 * 内部
	 */
	String FROM_IN = "Y";

	/**
	 * 标志
	 */
	String FROM = "from";

	/**
	 * OAUTH URL
	 */
	String OAUTH_TOKEN_URL = "/oauth/token";

	/**
	 * 手机号短信登录URL
	 */
	String PHONE_SMS_TOKEN_URL = "/phone/token/sms";

	/**
	 * 第三方登录URL
	 */
	String THIRDPARTY_TOKEN_URL = "/thirdparty/token";

	/**
	 * {bcrypt} 加密的特征码
	 */
	String BCRYPT = "{bcrypt}";
	/**
	 * sys_oauth_client 表的字段，id、client_secret
	 */
	String CLIENT_FIELDS = "id, CONCAT('{noop}',client_secret) as client_secret, resource_ids, scope, "
			+ "authorized_grant_types, web_server_redirect_uri, authorities, access_token_validity, "
			+ "refresh_token_validity, additional_information, autoapprove";

	/**
	 * JdbcClientDetailsService 查询语句
	 */
	String BASE_FIND_STATEMENT = "select " + CLIENT_FIELDS
			+ " from sys_oauth_client";

	/**
	 * 默认的查询语句
	 */
	String DEFAULT_FIND_STATEMENT = BASE_FIND_STATEMENT + " order by id";

	/**
	 * 按条件client_id 查询
	 */
	String DEFAULT_SELECT_STATEMENT = BASE_FIND_STATEMENT + " where id = ?";

	/**
	 * 资源服务器默认bean名称
	 */
	String RESOURCE_SERVER_CONFIGURER = "resourceServerConfigurerAdapter";

	/**
	 * 客户端模式
	 */
	String CLIENT_CREDENTIALS = "client_credentials";

	/**
	 * 用户ID字段
	 */
	String DETAILS_USER_ID = "user_id";

	/**
	 * 用户名字段
	 */
	String DETAILS_USERNAME = "username";

	/**
	 * 用户机构字段
	 */
	String DETAILS_ORGAN_ID = "organ_id";

	/**
	 * 用户类型
	 */
	String DETAILS_TYPE = "type";

	/**
	 * 租户ID 字段
	 */
	String DETAILS_TENANT_ID = "tenant_id";
	/**
	 * 平台系统管理员绑定的租户ID
	 */
	String DETAILS_TENANT_IDS = "tenant_ids";

	/**
	 * 切换到的租户ID 字段
	 */
	String SWITCH_TENANT_ID = "switch-tenant-id";

	/**
	 * 店铺ID 字段
	 */
	String DETAILS_SHOP_ID = "shop_id";

	/**
	 * 协议字段
	 */
	String DETAILS_LICENSE = "license";

	/**
	 * AES 加密
	 */
	String AES = "aes";
}
