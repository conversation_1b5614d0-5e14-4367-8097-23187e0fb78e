/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.LocalDateTimeUtils;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.data.tenant.TenantContextHolder;
import com.joolun.cloud.common.security.annotation.Inside;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.util.ApiUtil;
import com.joolun.cloud.mall.common.config.MallConfigProperties;
import com.joolun.cloud.mall.api.service.*;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.constant.MyReturnCode;
import com.joolun.cloud.mall.common.dto.PlaceOrderDTO;
import com.joolun.cloud.mall.common.dto.PlaceOrderSkuDTO;
import com.joolun.cloud.mall.common.entity.*;
import com.joolun.cloud.mall.common.enums.OrderInfoEnum;
import com.joolun.cloud.mall.common.feign.FeignJooLunPayService;
import com.joolun.cloud.mall.common.feign.FeignWxTemplateMsgService;
import com.joolun.cloud.pay.common.dto.AliBaseRequest;
import com.joolun.cloud.pay.common.entity.PayConfig;
import com.joolun.cloud.weixin.common.constant.ConfigConstant;
import com.joolun.cloud.weixin.common.dto.WxTemplateMsgSendDTO;
import com.joolun.cloud.weixin.common.entity.ThirdSession;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/orderinfo")
@Api(value = "orderinfo", tags = "商城订单API")
public class OrderInfoController {

    private final OrderInfoService orderInfoService;
	private final FeignJooLunPayService feignJooLunPayService;
	private final MallConfigProperties mallConfigProperties;
	private final OrderLogisticsService orderLogisticsService;
	private final GrouponInfoService grouponInfoService;
	private final GrouponUserService grouponUserService;
	private final BargainUserService bargainUserService;
	private final ShopInfoService shopInfoService;
	private final SeckillInfoService seckillInfoService;
	private final SeckillHallInfoService seckillHallInfoService;
	private final SeckillHallService seckillHallService;
	private final FeignWxTemplateMsgService feignWxTemplateMsgService;

	/**
	* 分页查询
	* @param page 分页对象
	* @param orderInfo 商城订单
	* @return
	*/
	@ApiOperation(value = "分页查询")
    @GetMapping("/page")
	@ApiLogin
    public R getOrderInfoPage(Page page, OrderInfo orderInfo) {
		orderInfo.setUserId(ThirdSessionHolder.getUserId());
        return R.ok(orderInfoService.page2(page,orderInfo));
    }

    /**
    * 通过id查询商城订单
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询商城订单")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id){
		return R.ok(orderInfoService.getById2(id));
    }

    /**
    * 新增商城订单
    * @param placeOrderDTO 商城订单
    * @return R
    */
	@ApiOperation(value = "新增商城订单")
    @PostMapping
	@ApiLogin
    public R orderSub(HttpServletRequest request, @RequestBody PlaceOrderDTO placeOrderDTO){
		placeOrderDTO.setUserId(ThirdSessionHolder.getUserId());
		placeOrderDTO.setAppId(ThirdSessionHolder.getThirdSession().getAppId());
		SeckillInfo seckillInfo = null;
		//秒杀订单处理
		if(MallConstants.ORDER_TYPE_3.equals(placeOrderDTO.getOrderType())){
			seckillInfo = seckillInfoService.getById(placeOrderDTO.getMarketId());
			if(seckillInfo.getLimitNum() <= seckillInfo.getSeckillNum()){
				return R.failed(MyReturnCode.ERR_80031.getCode(), MyReturnCode.ERR_80031.getMsg());
			}
			//查出当前用户已经购买该秒杀商品的次数
			int count = orderInfoService.count(Wrappers.<OrderInfo>lambdaQuery()
					.eq(OrderInfo::getUserId,ThirdSessionHolder.getUserId())
					.eq(OrderInfo::getOrderType,MallConstants.ORDER_TYPE_3)
					.eq(OrderInfo::getMarketId,placeOrderDTO.getMarketId())
					.and(wrapper -> wrapper.ne(OrderInfo::getStatus,OrderInfoEnum.STATUS_5.getValue())
						.or()
						.isNull(OrderInfo::getStatus)));
			if(count >= seckillInfo.getEachLimitNum()){
				return R.failed(MyReturnCode.ERR_80030.getCode(), MyReturnCode.ERR_80030.getMsg());
			}
			//校验秒杀合法性
			int seckillHallInfoCount = seckillHallInfoService.count(Wrappers.<SeckillHallInfo>lambdaQuery()
					.eq(SeckillHallInfo::getSeckillHallId,placeOrderDTO.getRelationId())
					.eq(SeckillHallInfo::getSeckillInfoId,placeOrderDTO.getMarketId()));
			if(seckillHallInfoCount <= 0){
				return R.failed(MyReturnCode.ERR_80032.getCode(), MyReturnCode.ERR_80032.getMsg());
			}
			//校验秒杀时间
			SeckillHall seckillHall = seckillHallService.getById(placeOrderDTO.getRelationId());
			if(seckillHall.getHallTime() != LocalDateTime.now().getHour()){
				return R.failed(MyReturnCode.ERR_80033.getCode(), MyReturnCode.ERR_80033.getMsg());
			}
		}
		placeOrderDTO.setAppType(ApiUtil.getClientType(request));
		placeOrderDTO.setPaymentWay(MallConstants.PAYMENT_WAY_2);
		List<OrderInfo> listOrderInfo = orderInfoService.orderSub(placeOrderDTO, seckillInfo);
		if(CollectionUtil.isEmpty(listOrderInfo)){
			return R.failed(MyReturnCode.ERR_70003.getCode(), MyReturnCode.ERR_70003.getMsg());
		}
		listOrderInfo.forEach(orderInfo -> {
			//发送微信模板消息
			try {
				WxTemplateMsgSendDTO wxTemplateMsgSendDTO = new WxTemplateMsgSendDTO();
				wxTemplateMsgSendDTO.setMallUserId(orderInfo.getUserId());
				wxTemplateMsgSendDTO.setUseType(ConfigConstant.WX_TMP_USE_TYPE_1);
				wxTemplateMsgSendDTO.setPage("pages/order/order-detail/index?id="+orderInfo.getId());
				Map<String, Object> data = new HashMap();
				Map<String, Object> map = BeanUtil.beanToMap(orderInfo);
				for(String mapKey: map.keySet()){
					data.put("order." + mapKey, map.get(mapKey));
				}
				if(orderInfo.getOrderLogistics() != null){
					Map<String, Object> map2 = BeanUtil.beanToMap(orderInfo.getOrderLogistics());
					for(String mapKey: map2.keySet()){
						data.put("orderLogistics." + mapKey, map2.get(mapKey));
					}
				}
				wxTemplateMsgSendDTO.setData(data);
				feignWxTemplateMsgService.sendTemplateMsgMp(wxTemplateMsgSendDTO, SecurityConstants.FROM_IN);
			}catch (Exception e){
				log.error("发送微信模板消息出错："+e.getMessage(), e);
			}
		});
		return R.ok(listOrderInfo);
    }

    /**
    * 通过id删除商城订单
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id删除商城订单")
    @DeleteMapping("/{id}")
    public R removeById(@PathVariable String id){
		OrderInfo orderInfo = orderInfoService.getById(id);
		if(orderInfo == null){
			return R.failed(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		if(!OrderInfoEnum.STATUS_5.getValue().equals(orderInfo.getStatus()) || CommonConstants.YES.equals(orderInfo.getIsPay())){
			return R.failed(MyReturnCode.ERR_70001.getCode(), MyReturnCode.ERR_70001.getMsg());
		}
		return R.ok(orderInfoService.removeById(id));
    }

	/**
	 * 取消商城订单
	 * @param id 商城订单
	 * @return R
	 */
	@ApiOperation(value = "取消商城订单")
	@PutMapping("/cancel/{id}")
	public R orderCancel(@PathVariable String id){
		OrderInfo orderInfo = orderInfoService.getById(id);
		if(orderInfo == null){
			return R.failed(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		if(!CommonConstants.NO.equals(orderInfo.getIsPay())){//只有未支付订单能取消
			return R.failed(MyReturnCode.ERR_70001.getCode(), MyReturnCode.ERR_70001.getMsg());
		}
		orderInfoService.orderCancel(orderInfo);
		return R.ok();
	}

	/**
	 * 取消商城订单（服务间调用）
	 * @param id 商城订单
	 * @return R
	 */
	@ApiOperation(value = "取消商城订单")
	@PutMapping("/inside/cancel/{id}")
	@Inside
	public R orderCancelInside(@PathVariable String id){
		OrderInfo orderInfo = orderInfoService.getById(id);
		if(orderInfo == null){
			return R.failed(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		if(!CommonConstants.NO.equals(orderInfo.getIsPay())){//只有未支付订单能取消
			return R.failed(MyReturnCode.ERR_70001.getCode(), MyReturnCode.ERR_70001.getMsg());
		}
		orderInfoService.orderCancel(orderInfo);
		return R.ok();
	}

	/**
	 * 商城订单确认收货
	 * @param id 商城订单
	 * @return R
	 */
	@ApiOperation(value = "商城订单确认收货")
	@PutMapping("/receive/{id}")
	public R orderReceive(@PathVariable String id){
		OrderInfo orderInfo = orderInfoService.getById(id);
		if(orderInfo == null){
			return R.failed(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		if(!OrderInfoEnum.STATUS_2.getValue().equals(orderInfo.getStatus())){//只有待收货订单能确认收货
			return R.failed(MyReturnCode.ERR_70001.getCode(), MyReturnCode.ERR_70001.getMsg());
		}
		orderInfoService.orderReceive(orderInfo);
		return R.ok();
	}

	/**
	 * 调用统一下单接口，并组装生成支付所需参数对象.
	 *
	 * @param orderInfo 统一下单请求参数
	 * @return 返回 {@link com.github.binarywang.wxpay.bean.order}包下的类对象
	 */
	@ApiOperation(value = "调用统一下单接口")
	@PostMapping("/unifiedOrder")
	@ApiLogin
	public R unifiedOrder(@RequestBody OrderInfo orderInfo){
		String returnUrl = orderInfo.getReturnUrl();
		String quitUrl = orderInfo.getQuitUrl();
		String tradeType = orderInfo.getTradeType();
		String paymentType = orderInfo.getPaymentType();
		ThirdSession thirdSession = ThirdSessionHolder.getThirdSession();
		orderInfo = orderInfoService.getById(orderInfo.getId());
		if(orderInfo == null){
			return R.failed(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		if(!CommonConstants.NO.equals(orderInfo.getIsPay())){//只有未支付的详单能发起支付
			return R.failed(MyReturnCode.ERR_70004.getCode(), MyReturnCode.ERR_70004.getMsg());
		}
		if(MallConstants.ORDER_TYPE_1.equals(orderInfo.getOrderType())) {//砍价订单
			BargainUser bargainUser = bargainUserService.getById(orderInfo.getRelationId());
			if(CommonConstants.YES.equals(bargainUser.getIsBuy())){
				return R.failed(MyReturnCode.ERR_80006.getCode(), MyReturnCode.ERR_80006.getMsg());
			}
		}
		if(MallConstants.ORDER_TYPE_2.equals(orderInfo.getOrderType())){//拼团订单
			GrouponInfo grouponInfo = grouponInfoService.getOne(Wrappers.<GrouponInfo>lambdaQuery()
					.eq(GrouponInfo::getId,orderInfo.getMarketId())
					.eq(GrouponInfo::getEnable,CommonConstants.YES)
					.lt(GrouponInfo::getValidBeginTime,LocalDateTime.now())
					.gt(GrouponInfo::getValidEndTime,LocalDateTime.now()));
			if(grouponInfo == null){//判断拼团的有效性
				return R.failed(MyReturnCode.ERR_80010.getCode(), MyReturnCode.ERR_80010.getMsg());
			}
			if(StrUtil.isNotBlank(orderInfo.getRelationId())){
				//校验当前用户是否已经参与
				GrouponUser grouponUser1 = grouponUserService.getOne(Wrappers.<GrouponUser>lambdaQuery()
						.eq(GrouponUser::getGroupId,orderInfo.getRelationId())
						.eq(GrouponUser::getUserId,thirdSession.getUserId()));
				if(grouponUser1 != null){
					return R.failed(MyReturnCode.ERR_80012.getCode(), MyReturnCode.ERR_80012.getMsg());
				}
				//校验拼团人数
				GrouponUser grouponUser = grouponUserService.getById(orderInfo.getRelationId());
				Integer havCountGrouponing = grouponUserService.selectCountGrouponing(orderInfo.getRelationId());
				if(havCountGrouponing >= grouponUser.getGrouponNum()){
					return R.failed(MyReturnCode.ERR_80011.getCode(), MyReturnCode.ERR_80011.getMsg());
				}
			}
		}
		ShopInfo shopInfo = shopInfoService.getById(orderInfo.getShopId());
		if(CommonConstants.NO.equals(shopInfo.getEnable())){
			return R.failed(MyReturnCode.ERR_80044.getCode(), MyReturnCode.ERR_80044.getMsg());
		}
		if(orderInfo.getPaymentPrice().compareTo(BigDecimal.ZERO)==0){//0元购买不调支付
			orderInfo.setPaymentTime(LocalDateTime.now());
			orderInfoService.notifyOrder(orderInfo);
			return R.ok();
		}
		if(StrUtil.isBlank(paymentType)){
			return R.failed(MyReturnCode.ERR_70002.getCode(), MyReturnCode.ERR_70002.getMsg());
		}
		String body = orderInfo.getName();
		body = body.length() > 40 ? body.substring(0,39) : body;
		//微信支付
		if(MallConstants.ORDER_PAYMENT_TYPE_1.equals(paymentType)){
			//更新编号防止不同终端微信报重复订单号
			orderInfo.setOrderNo(IdUtil.getSnowflake(0,0).nextIdStr());
			orderInfoService.updateById(orderInfo);
			WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();
			wxPayUnifiedOrderRequest.setSubMchId(shopInfo.getWxMchId());
			wxPayUnifiedOrderRequest.setBody(body);
			wxPayUnifiedOrderRequest.setOutTradeNo(orderInfo.getOrderNo());
			wxPayUnifiedOrderRequest.setTotalFee(orderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue());
			wxPayUnifiedOrderRequest.setTradeType(tradeType);
			wxPayUnifiedOrderRequest.setNotifyUrl(mallConfigProperties.getNotifyHost()+MallConstants.SERVER_ROUTE_NAME_MALLAPI+MallConstants.ORDER_NOTIFY_URL_WX);
			wxPayUnifiedOrderRequest.setSpbillCreateIp("127.0.0.1");
			wxPayUnifiedOrderRequest.setSubOpenid(thirdSession.getOpenId());
			wxPayUnifiedOrderRequest.setSubAppId(thirdSession.getAppId());
			return feignJooLunPayService.unifiedOrderWx(wxPayUnifiedOrderRequest, SecurityConstants.FROM_IN);
		}else if(MallConstants.ORDER_PAYMENT_TYPE_2.equals(paymentType)){//支付宝支付
			Map<String, Object> params = new HashMap<>();
			params.put("tradeType",tradeType);
			params.put("outTradeNo",orderInfo.getOrderNo());
			params.put("totalAmount",orderInfo.getPaymentPrice().toString());
			params.put("notifyUrl",mallConfigProperties.getNotifyHost()+MallConstants.SERVER_ROUTE_NAME_MALLAPI+MallConstants.ORDER_NOTIFY_URL_ALI);
			params.put("returnUrl",returnUrl);
			params.put("quitUrl",quitUrl);
			params.put("subject",body);
			AliBaseRequest aliBaseRequest = new AliBaseRequest();
			aliBaseRequest.setAppAuthToken(shopInfo.getAliAuthToken());
			aliBaseRequest.setParams(params);
			return feignJooLunPayService.unifiedOrderAli(aliBaseRequest, SecurityConstants.FROM_IN);
		}else{
			return R.failed("无此支付类型");
		}
	}

	/**
	 * 支付回调（微信）
	 * @param xmlData
	 * @return
	 * @throws WxPayException
	 */
	@ApiOperation(value = "支付回调（微信）")
	@PostMapping("/notify-order-wx")
	public String notifyOrderWx(@RequestBody String xmlData) {
		log.info("支付回调（微信）:"+xmlData);
		WxPayOrderNotifyResult rs = WxPayOrderNotifyResult.fromXML(xmlData);
		PayConfig payConfig = feignJooLunPayService.getPayConfig(rs.getAppid(), SecurityConstants.FROM_IN).getData();
		TenantContextHolder.setTenantId(payConfig.getTenantId());//设置租户ID
		R<WxPayOrderNotifyResult> r = feignJooLunPayService.notifyOrderWx(xmlData, SecurityConstants.FROM_IN);
		if(r.isOk()){
			WxPayOrderNotifyResult notifyResult = r.getData();
			OrderInfo orderInfo = orderInfoService.getOne(Wrappers.<OrderInfo>lambdaQuery()
					.eq(OrderInfo::getOrderNo,notifyResult.getOutTradeNo()));
			if(orderInfo != null){
				orderInfo.setTradeType(notifyResult.getTradeType());
				if(orderInfo.getPaymentPrice().multiply(new BigDecimal(100)).intValue() == notifyResult.getTotalFee()){
					String timeEnd = notifyResult.getTimeEnd();
					LocalDateTime paymentTime = LocalDateTimeUtils.parse(timeEnd);
					orderInfo.setPaymentTime(paymentTime);
					orderInfo.setTransactionId(notifyResult.getTransactionId());
					orderInfo.setPaymentType(MallConstants.ORDER_PAYMENT_TYPE_1);
					orderInfoService.notifyOrder(orderInfo);
					return WxPayNotifyResponse.success("成功");
				}else{
					return WxPayNotifyResponse.fail("付款金额与订单金额不等");
				}
			}else{
				return WxPayNotifyResponse.fail("无此订单");
			}
		}else{
			return WxPayNotifyResponse.fail(r.getMsg());
		}
	}

	/**
	 * 支付回调（支付宝）
	 * @param request
	 * @return
	 * @throws
	 */
	@ApiOperation(value = "支付回调（支付宝）")
	@ResponseBody
	@RequestMapping("/notify-order-ali")
	public String notifyOrderAli(HttpServletRequest request) {
		log.info("支付回调（支付宝）");
		Map<String, String> params = new HashMap<>();
		Map requestParams = request.getParameterMap();
		for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
			String name = (String) iter.next();
			String[] values = (String[]) requestParams.get(name);
			String valueStr = "";
			for (int i = 0; i < values.length; i++) {
				valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
			}
			params.put(name, valueStr);
		}
		String appId = request.getParameter("app_id");
		if(StrUtil.isBlank(appId)){
			return "appId不能为空";
		}
		PayConfig payConfig = feignJooLunPayService.getPayConfig(appId, SecurityConstants.FROM_IN).getData();
		TenantContextHolder.setTenantId(payConfig.getTenantId());//设置租户ID
		OrderInfo orderInfo = orderInfoService.getOne(Wrappers.<OrderInfo>lambdaQuery()
				.eq(OrderInfo::getOrderNo,request.getParameter("out_trade_no")));
		if(orderInfo == null){
			log.error("无此订单out_trade_no："+request.getParameter("out_trade_no"));
			return "无此订单";
		}
//		orderInfo.setTradeType(notifyResult.getTradeType());
		if(orderInfo.getPaymentPrice().compareTo(new BigDecimal(request.getParameter("total_amount"))) == 0){
			LocalDateTime paymentTime = LocalDateTimeUtils.parse(request.getParameter("gmt_payment"));
			orderInfo.setPaymentTime(paymentTime);
			orderInfo.setTransactionId(request.getParameter("trade_no"));
			orderInfo.setPaymentType(MallConstants.ORDER_PAYMENT_TYPE_2);
			orderInfoService.notifyOrder(orderInfo);
			log.info("支付回调（支付宝）success");
			return "success";
		}else{
			log.error("付款金额与订单金额不等out_trade_no："+request.getParameter("out_trade_no"));
			return "付款金额与订单金额不等";
		}

	}

	/**
	 * 物流信息回调
	 * @param request
	 * @return
	 */
	@ApiOperation(value = "物流信息回调")
	@PostMapping("/notify-logisticsr")
	public String notifyLogisticsr(HttpServletRequest request, HttpServletResponse response) {
		String param = request.getParameter("param");
		String logisticsId = request.getParameter("logisticsId");
		String tenantId = request.getParameter("tenantId");
		TenantContextHolder.setTenantId(tenantId);
		log.info("物流信息回调:"+param);
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("result",false);
		map.put("returnCode","500");
		map.put("message","保存失败");
		try {
			JSONObject jsonObject = JSONUtil.parseObj(param);
			orderInfoService.notifyLogisticsr(logisticsId, jsonObject);
			map.put("result",true);
			map.put("returnCode","200");
			map.put("message","保存成功");
			//这里必须返回，否则认为失败，过30分钟又会重复推送。
			response.getWriter().print(JSONUtil.parseObj(map));
		} catch (Exception e) {
			map.put("message","保存失败" + e.getMessage());
			//保存失败，服务端等30分钟会重复推送。
			try {
				response.getWriter().print(JSONUtil.parseObj(map));
			} catch (Exception e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 通过物流id查询订单物流
	 * @param logisticsId
	 * @return R
	 */
	@ApiOperation(value = "通过物流id查询订单物流")
	@GetMapping("/orderlogistics/{logisticsId}")
	public R getOrderLogistics(@PathVariable("logisticsId") String logisticsId){
		return R.ok(orderLogisticsService.getById(logisticsId));
	}

	/**
	 * 统计各个状态订单计数
	 * @param orderInfo
	 * @return R
	 */
	@ApiOperation(value = "统计各个状态订单计数")
	@GetMapping("/countAll")
	@ApiLogin
	public R count(OrderInfo orderInfo){
		orderInfo.setUserId(ThirdSessionHolder.getUserId());
		Map<String, Integer> countAll = new HashMap<>();
		countAll.put(OrderInfoEnum.STATUS_0.getValue(),orderInfoService.count(Wrappers.query(orderInfo).lambda()
				.isNull(OrderInfo::getStatus)
				.eq(OrderInfo::getIsPay,CommonConstants.NO)));

		countAll.put(OrderInfoEnum.STATUS_1.getValue(),orderInfoService.count(Wrappers.query(orderInfo).lambda()
				.eq(OrderInfo::getStatus,OrderInfoEnum.STATUS_1.getValue())
				.eq(OrderInfo::getIsPay,CommonConstants.YES)));

		countAll.put(OrderInfoEnum.STATUS_2.getValue(),orderInfoService.count(Wrappers.query(orderInfo).lambda()
				.eq(OrderInfo::getStatus,OrderInfoEnum.STATUS_2.getValue())
				.eq(OrderInfo::getIsPay,CommonConstants.YES)));

		countAll.put(OrderInfoEnum.STATUS_3.getValue(),orderInfoService.count(Wrappers.query(orderInfo).lambda()
				.eq(OrderInfo::getStatus,OrderInfoEnum.STATUS_3.getValue())
				.eq(OrderInfo::getIsPay,CommonConstants.YES)
				.eq(OrderInfo::getAppraisesStatus,MallConstants.APPRAISES_STATUS_0)));
		return R.ok(countAll);
	}


	/**
	 * listSkuInOrderSub
	 * @param skus
	 * @return R
	 */
	@ApiOperation(value = "listSkuInOrderSub")
	@Inside
	@PostMapping("/inside/listSkuInOrderSub")
	public R<List<GoodsSku>> listSkuInOrderSub(@RequestBody List<PlaceOrderSkuDTO> skus){
		return R.ok(orderInfoService.listSkuInOrderSub(skus));
	}


	/**
	 * getCouponUserId
	 * @param couponUserId
	 * @return R
	 */
	@ApiOperation(value = "getCouponUserId")
	@Inside
	@GetMapping("/inside/getCouponUserId")
	public R<CouponUser> getCouponUserId( String couponUserId){
		CouponUser couponUser=orderInfoService.getCouponUserByCouponUserId(couponUserId);
		return R.ok(couponUser);
	}

	/**
	 * getCouponUserId
	 * @param goodsSkuId
	 * @return R
	 */
	@ApiOperation(value = "getListGoodsSkuSpecValueBySkuId")
	@Inside
	@GetMapping("/inside/getListGoodsSkuSpecValueBySkuId")
	public R<List<GoodsSkuSpecValue>> getListGoodsSkuSpecValueBySkuId( String goodsSkuId){
		List<GoodsSkuSpecValue> listGoodsSkuSpecValueBySkuId = orderInfoService.getListGoodsSkuSpecValueBySkuId(goodsSkuId);
		return R.ok(listGoodsSkuSpecValueBySkuId);
	}

	/**
	 * listSkuInOrderSub
	 *
	 * @param listCouponUser
	 * @return R
	 */
	@ApiOperation(value = "updateBatchCouponUserByListCouponUser")
	@Inside
	@PostMapping("/inside/updateBatchCouponUserByListCouponUser")
	public R<Boolean> updateBatchCouponUserByListCouponUser(@RequestBody List<CouponUser> listCouponUser){
		return R.ok(orderInfoService.updateBatchCouponUserByListCouponUser(listCouponUser));
	}

}
