/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.weixin.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.weixin.admin.mapper.OrderRefundsMapper;
import com.joolun.cloud.weixin.admin.service.OrderInfoService;
import com.joolun.cloud.weixin.admin.service.OrderItemService;
import com.joolun.cloud.weixin.admin.service.OrderRefundsService;
import com.joolun.cloud.weixin.admin.service.ShopInfoService;
import com.joolun.cloud.weixin.common.constant.WxConstants;
import com.joolun.cloud.weixin.common.entity.OrderInfo;
import com.joolun.cloud.weixin.common.entity.OrderItem;
import com.joolun.cloud.weixin.common.entity.OrderRefunds;
import com.joolun.cloud.weixin.common.entity.ShopInfo;
import com.joolun.cloud.weixin.common.enums.OrderInfoEnum;
import com.joolun.cloud.weixin.common.enums.OrderItemEnum;
import com.joolun.cloud.weixin.common.enums.OrderRefundsEnum;
import com.joolun.cloud.weixin.common.feign.FeignJooLunPayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 退款详情
 *
 * <AUTHOR>
 * @date 2019-11-14 16:35:25
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderRefundsServiceImpl extends ServiceImpl<OrderRefundsMapper, OrderRefunds> implements OrderRefundsService {

    private final OrderInfoService orderInfoService;
    private final OrderItemService orderItemService;
    private final FeignJooLunPayService feignJooLunPayService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean doOrderRefunds(OrderRefunds entity) {
        OrderRefunds orderRefunds = baseMapper.selectById(entity.getId());
        if (orderRefunds != null) {
            if (OrderRefundsEnum.STATUS_11.getValue().equals(entity.getStatus()) || OrderRefundsEnum.STATUS_211.getValue().equals(entity.getStatus())) {
                //修改退款状态
                orderRefunds.setStatus(entity.getStatus());
                orderRefunds.setRefuseRefundReson(entity.getRefuseRefundReson());
                baseMapper.updateById(orderRefunds);

                if (orderRefunds.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {//退款金额大于0
                    OrderItem orderItem = orderItemService.getById2(orderRefunds.getOrderItemId());
                    OrderInfo orderInfo = orderInfoService.getById(orderItem.getOrderId());
                    //校验数据，只有已支付的订单、未退款的订单详情才能退款
                    if (CommonConstants.YES.equals(orderInfo.getIsPay())
                            && CommonConstants.NO.equals(orderItem.getIsRefund())
                            && orderRefunds.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                        //获取支付总金额
                        AtomicReference<BigDecimal> totalFee = new AtomicReference<>(BigDecimal.ZERO);
                        List<OrderInfo> listOrderInfo = orderInfoService.list(Wrappers.<OrderInfo>query().lambda()
                                .eq(OrderInfo::getTransactionId, orderInfo.getTransactionId()));
                        listOrderInfo.forEach(orderInfo1 -> {
                            totalFee.set(totalFee.get().add(orderInfo1.getPaymentPrice()));
                        });
                        //发起微信退款申请
//						if("1".equals(orderInfo.getPaymentType())){
                        WxPayRefundRequest request = new WxPayRefundRequest();
                        request.setSubMchId(orderInfo.getShopId());
                        request.setTransactionId(orderInfo.getTransactionId());
                        request.setOutRefundNo(orderRefunds.getId());
                        request.setTotalFee(totalFee.get().multiply(new BigDecimal(100)).intValue());
                        request.setRefundFee(orderRefunds.getRefundAmount().multiply(new BigDecimal(100)).intValue());
                        request.setNotifyUrl(WxConstants.NOTIFY_HOST +"/weixinapi/orderinfo/notify-refunds");
                        R<WxPayRefundResult> r = feignJooLunPayService.refundOrderWx(request, SecurityConstants.FROM_IN);
                        if (r.isOk()) {
                            entity.setRefundTradeNo(r.getData().getRefundId());
                        } else {
                            throw new RuntimeException(r.getMsg());
                        }
//						}
                    }
                }
            } else {
                //修改退款状态
                orderRefunds.setStatus(entity.getStatus());
                orderRefunds.setRefuseRefundReson(entity.getRefuseRefundReson());
                baseMapper.updateById(orderRefunds);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doOrderRefundsResult(OrderRefunds orderRefunds) {
        //修改订单详情退款状态为已退款
        OrderItem orderItem = orderItemService.getById(orderRefunds.getOrderItemId());
        if ((OrderRefundsEnum.STATUS_11.getValue().equals(orderRefunds.getStatus()) || OrderRefundsEnum.STATUS_211.getValue().equals(orderRefunds.getStatus()))
                && CommonConstants.NO.equals(orderItem.getIsRefund())) {
            orderItem.setIsRefund(CommonConstants.YES);
            String status = "";
            if (OrderRefundsEnum.STATUS_11.getValue().equals(orderRefunds.getStatus())) {
                status = OrderItemEnum.STATUS_3.getValue();
            }
            if (OrderRefundsEnum.STATUS_211.getValue().equals(orderRefunds.getStatus())) {
                status = OrderItemEnum.STATUS_4.getValue();
            }
            orderItem.setStatus(status);
            orderItemService.updateById(orderItem);

            OrderInfo orderInfo ;
            List<OrderItem> listOrderItem = orderItemService.list(Wrappers.<OrderItem>query().lambda()
                    .eq(OrderItem::getOrderId, orderItem.getOrderId()));
            List<OrderItem> listOrderItem2 = listOrderItem.stream()
                    .filter(obj -> !obj.getId().equals(orderRefunds.getOrderItemId()) && CommonConstants.NO.equals(obj.getIsRefund())).collect(Collectors.toList());
            //如果订单下面所有订单详情都退款了，则取消订单
            if (listOrderItem2.size() <= 0) {
                orderInfo = new OrderInfo();
                orderInfo.setId(orderItem.getOrderId());
                orderInfo.setStatus(OrderInfoEnum.STATUS_5.getValue());
                orderInfoService.updateById(orderInfo);
            }
        }
    }

    @Override
    public IPage<OrderRefunds> page1(IPage<OrderRefunds> page, OrderRefunds orderRefunds) {
        return baseMapper.selectPage1(page, orderRefunds);
    }
}
