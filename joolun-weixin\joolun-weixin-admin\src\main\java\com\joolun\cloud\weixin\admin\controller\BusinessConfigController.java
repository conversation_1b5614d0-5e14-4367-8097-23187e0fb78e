package com.joolun.cloud.weixin.admin.controller;

import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.weixin.admin.service.BusinessConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 业务配置
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/business-config")
@Api(value = "business-config", tags = "业务配置管理")
public class BusinessConfigController {

    private final BusinessConfigService businessConfigService;

    /**
     * 获取业务配置
     * @return R
     */
    @ApiOperation(value = "获取业务配置")
    @GetMapping
    @PreAuthorize("@ato.hasAuthority('wx:businessconfig:get')")
    public R getConfigs() {
        return R.ok(businessConfigService.getAllConfigs());
    }

    /**
     * 更新业务配置
     * @param configs 配置项Map
     * @return R
     */
    @ApiOperation(value = "更新业务配置")
    @SysLog("更新业务配置")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('wx:businessconfig:edit')")
    public R updateConfigs(@RequestBody Map<String, Object> configs) {
        boolean result = businessConfigService.updateConfigs(configs);
        return result ? R.ok("配置更新成功") : R.failed("配置更新失败");
    }

    /**
     * 获取配置项列表（包含完整信息）
     * @return R
     */
    @ApiOperation(value = "获取配置项列表")
    @GetMapping("/list")
    @PreAuthorize("@ato.hasAuthority('wx:businessconfig:get')")
    public R getConfigList() {
        return R.ok(businessConfigService.getAllConfigList());
    }
}
