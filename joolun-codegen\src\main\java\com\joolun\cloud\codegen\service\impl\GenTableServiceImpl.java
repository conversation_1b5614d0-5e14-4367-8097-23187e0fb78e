/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.codegen.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.codegen.entity.GenTable;
import com.joolun.cloud.codegen.mapper.GenTableMapper;
import com.joolun.cloud.codegen.service.GenTableService;
import org.springframework.stereotype.Service;

/**
 * 代码生成配置表
 *
 * <AUTHOR>
 * @date 2020-04-07 19:14:52
 */
@Service
public class GenTableServiceImpl extends ServiceImpl<GenTableMapper, GenTable> implements GenTableService {

}
