package com.joolun.cloud.weixin.api.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.aliyuncs.exceptions.ClientException;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.weixin.api.service.OrderItemService;
import com.joolun.cloud.weixin.api.service.PhoneService;
import com.joolun.cloud.weixin.common.entity.OrderItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 手机验证码
 */
@RestController
@AllArgsConstructor
@RequestMapping("/orderitem")
@Api(value = "orderitem", tags = "商城订单详情API")
public class OrderItemController {

	private final OrderItemService orderItemService;

	/**
	 * 通过id查询商城订单详情
	 * @param id
	 * @return R
	 */
	@ApiOperation(value = "通过id查询商城订单详情")
	@GetMapping("/{id}")
	public R<OrderItem> getById(@PathVariable("id") String id){
		return R.ok(orderItemService.getById2(id));
	}

}
