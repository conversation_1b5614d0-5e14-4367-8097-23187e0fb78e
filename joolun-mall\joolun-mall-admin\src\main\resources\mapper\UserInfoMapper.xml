<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2018-2019
  ~ All rights reserved, Designed By www.joolun.com
  ~ 注意：
  ~ 本软件为www.joolun.com开发研制，未经购买不得使用
  ~ 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  ~ 一经发现盗用、分享等行为，将追究法律责任，后果自负
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.mall.admin.mapper.UserInfoMapper">

	<resultMap id="userInfoMap" type="com.joolun.cloud.mall.common.entity.UserInfo">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="userCode" column="user_code"/>
		<result property="phone" column="phone"/>
		<result property="password" column="password"/>
		<result property="appType" column="app_type"/>
		<result property="appId" column="app_id"/>
		<result property="userGrade" column="user_grade"/>
		<result property="pointsCurrent" column="points_current"/>
		<result property="nickName" column="nick_name"/>
		<result property="sex" column="sex"/>
		<result property="headimgUrl" column="headimg_url"/>
		<result property="city" column="city"/>
		<result property="country" column="country"/>
		<result property="province" column="province"/>
		<result property="parentId" column="parent_id"/>
		<result property="version" column="version"/>
	</resultMap>

	<resultMap id="userInfoMap1" extends="userInfoMap" type="com.joolun.cloud.mall.common.entity.UserInfo">
		<collection property="parentUserInfo" ofType="com.joolun.cloud.mall.common.entity.UserInfo"
					select="com.joolun.cloud.mall.admin.mapper.UserInfoMapper.selectById"
					column="{id=parent_id}">
		</collection>
		<collection property="distributionUser" ofType="com.joolun.cloud.mall.common.entity.DistributionUser"
					select="com.joolun.cloud.mall.admin.mapper.DistributionUserMapper.selectById"
					column="{userId=id}">
		</collection>
	</resultMap>

	<sql id="userInfoSql">
        user_info.`id`,
		user_info.`tenant_id`,
		user_info.`del_flag`,
		user_info.`create_time`,
		user_info.`update_time`,
		user_info.`user_code`,
		user_info.`app_type`,
		user_info.`app_id`,
		user_info.`phone`,
		user_info.`password`,
		user_info.`user_grade`,
		user_info.`points_current`,
		user_info.`nick_name`,
		user_info.`sex`,
		user_info.`headimg_url`,
		user_info.`city`,
		user_info.`country`,
		user_info.`province`,
		user_info.`parent_id`,
		user_info.`version`
    </sql>

	<select id="selectPage1" resultMap="userInfoMap1">
		SELECT
		<include refid="userInfoSql"/>
		FROM user_info user_info
		<where>
			<if test="query.userCode != null">
				AND user_info.`user_code` = #{query.userCode}
			</if>
			<if test="query.appType != null">
				AND user_info.`app_type` = #{query.appType}
			</if>
			<if test="query.appId != null">
				AND user_info.`app_id` = #{query.appId}
			</if>
			<if test="query.phone != null">
				AND user_info.`phone` = #{query.phone}
			</if>
			<if test="query.userGrade != null">
				AND user_info.`user_grade` = #{query.userGrade}
			</if>
			<if test="query.nickName != null">
				AND user_info.`nick_name` = #{query.nickName}
			</if>
			<if test="query.sex != null">
				AND user_info.`sex` = #{query.sex}
			</if>
			<if test="query.country != null">
				AND user_info.`country` = #{query.country}
			</if>
			<if test="query.city != null">
				AND user_info.`city` = #{query.city}
			</if>
			<if test="query.province != null">
				AND user_info.`province` = #{query.province}
			</if>
			<if test="query.parentId != null">
				AND user_info.`parent_id` = #{query.parentId}
			</if>
			<if test="query.parentSecondId != null">
				AND user_info.`parent_id` IN (SELECT `id` FROM `user_info` WHERE `parent_id` = #{query.parentSecondId})
			</if>
		</where>
	</select>
</mapper>
