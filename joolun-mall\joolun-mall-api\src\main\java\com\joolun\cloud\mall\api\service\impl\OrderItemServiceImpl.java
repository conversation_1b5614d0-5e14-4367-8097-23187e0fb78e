/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.common.entity.OrderItem;
import com.joolun.cloud.mall.api.mapper.OrderItemMapper;
import com.joolun.cloud.mall.api.service.OrderItemService;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * 商城订单详情
 *
 * <AUTHOR>
 * @date 2019-09-10 15:31:40
 */
@Service
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements OrderItemService {

	@Override
	public OrderItem getById2(Serializable id) {
		return baseMapper.selectById2(id);
	}
}
