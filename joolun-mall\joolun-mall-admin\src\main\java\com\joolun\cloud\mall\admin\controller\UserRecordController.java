/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.UserRecord;
import com.joolun.cloud.mall.admin.service.UserRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.math.BigDecimal;

/**
 * 用户消费记录
 *
 * <AUTHOR>
 * @date 2021-04-22 11:18:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/userrecord")
@Api(value = "userrecord", tags = "用户消费记录管理")
public class UserRecordController {

    private final UserRecordService userRecordService;

    /**
     * 用户消费记录分页列表
     * @param page 分页对象
     * @param userRecord 用户消费记录
     * @return
     */
    @ApiOperation(value = "用户消费记录分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:userrecord:index')")
    public R getPage(Page page, UserRecord userRecord) {
        return R.ok(userRecordService.page(page, Wrappers.query(userRecord)));
    }

    /**
     * 用户消费记录查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "用户消费记录查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:userrecord:get','mall:userinfo:get')")
    public R getById(@PathVariable("id") String id) {
		UserRecord userRecord = userRecordService.getById(id);
		if(userRecord == null){
			userRecord = new UserRecord();
			userRecord.setTotalAmount(BigDecimal.ZERO);
			userRecord.setTotalOrder(0);
		}
        return R.ok(userRecord);
    }

    /**
     * 用户消费记录新增
     * @param userRecord 用户消费记录
     * @return R
     */
    @ApiOperation(value = "用户消费记录新增")
    @SysLog("新增用户消费记录")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:userrecord:add')")
    public R save(@RequestBody UserRecord userRecord) {
        return R.ok(userRecordService.save(userRecord));
    }

    /**
     * 用户消费记录修改
     * @param userRecord 用户消费记录
     * @return R
     */
    @ApiOperation(value = "用户消费记录修改")
    @SysLog("修改用户消费记录")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:userrecord:edit')")
    public R updateById(@RequestBody UserRecord userRecord) {
        return R.ok(userRecordService.updateById(userRecord));
    }

    /**
     * 用户消费记录删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "用户消费记录删除")
    @SysLog("删除用户消费记录")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:userrecord:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(userRecordService.removeById(id));
    }

}
