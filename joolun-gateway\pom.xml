<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.joolun</groupId>
		<artifactId>joolun-plus</artifactId>
		<version>1.0.16</version>
	</parent>

	<artifactId>joolun-gateway</artifactId>
	<packaging>jar</packaging>

	<description>服务网关</description>

	<dependencies>
		<!--gateway 网关依赖,内置webflux 依赖-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-gateway</artifactId>
			<version>3.0.7</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis-reactive</artifactId>
		</dependency>
		<!--注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!-- SpringCloud Ailibaba Sentinel -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
		</dependency>
		<!-- SpringCloud Ailibaba Sentinel Gateway -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-alibaba-sentinel-gateway</artifactId>
		</dependency>
		<!-- Sentinel Datasource Nacos -->
		<dependency>
			<groupId>com.alibaba.csp</groupId>
			<artifactId>sentinel-datasource-nacos</artifactId>
		</dependency>
		<!--验证码-->
		<dependency>
			<groupId>com.github.axet</groupId>
			<artifactId>kaptcha</artifactId>
		</dependency>
		<dependency>
			<groupId>com.joolun</groupId>
			<artifactId>joolun-common-core</artifactId>
		</dependency>
		<!--nacos操作-->
		<dependency>
			<groupId>com.joolun</groupId>
			<artifactId>joolun-common-nacos</artifactId>
		</dependency>
		<!--缓存-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<!--接口文档-->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>swagger-bootstrap-ui</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
			</plugin>

			<!--打包时复制jar包到指定文件目录-->
			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id><!--需要唯一-->
						<phase>package</phase><!--当执行package操作时执行一下任务-->
						<configuration>
							<target><!--任务-->
								<echo message="start.................................."/><!--打印-->
								<echo message="load maven plugin ant-contrib-1.0b3"/>
								<!--加载plugin ant-contrib的配置文件-->
								<taskdef resource="net/sf/antcontrib/antlib.xml">
									<classpath><!--加载jar包,${settings.localRepository}的值是你maven settings文件中配置的本地仓库位置-->
										<pathelement location="${settings.localRepository}/ant-contrib/ant-contrib/1.0b3/ant-contrib-1.0b3.jar"/>
									</classpath>
								</taskdef>
								<!--复制jar包-->
								<if>
									<equals arg1="${copy}" arg2="true"/><!--是否复制jar包-->
									<then>
										<echo message="Copy jar to your desired path."/>
										<copy todir="${localDir}" overwrite="true"><!--执行复制操作,todir的值是将要复制jar包到的地方,overwrite是否重写-->
											<fileset dir="${project.build.directory}"><!--${project.build.directory}值是你的target目录-->
												<include name="*.jar"/><!--target目录下的jar包-->
											</fileset>
										</copy>
									</then>
								</if>
							</target>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
