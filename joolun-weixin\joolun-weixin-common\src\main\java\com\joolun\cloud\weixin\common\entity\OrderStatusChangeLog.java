package com.joolun.cloud.weixin.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 订单状态变更日志
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@TableName("order_status_change_log")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "订单状态变更日志")
public class OrderStatusChangeLog extends Model<OrderStatusChangeLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户")
    private String tenantId;

    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    private String modifierId;

    /**
     * 修改人姓名
     */
    @ApiModelProperty(value = "修改人姓名")
    private String modifierName;

    /**
     * 原状态
     */
    @ApiModelProperty(value = "原状态")
    private String oldStatus;

    /**
     * 新状态
     */
    @ApiModelProperty(value = "新状态")
    private String newStatus;

    /**
     * 变更原因/备注
     */
    @ApiModelProperty(value = "变更原因/备注")
    private String changeReason;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标记（0：显示；1：隐藏）
     */
    @ApiModelProperty(value = "逻辑删除标记")
    private String delFlag;
}
