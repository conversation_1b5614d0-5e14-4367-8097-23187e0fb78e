<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.joolun</groupId>
		<artifactId>joolun-common</artifactId>
		<version>1.0.16</version>
	</parent>

	<artifactId>joolun-common-log</artifactId>
	<packaging>jar</packaging>

	<description>日志服务</description>


	<dependencies>
		<!--工具类核心包-->
		<dependency>
			<groupId>com.joolun</groupId>
			<artifactId>joolun-common-core</artifactId>
		</dependency>
		<!--UPMS接口模块-->
		<dependency>
			<groupId>com.joolun</groupId>
			<artifactId>joolun-upms-common</artifactId>
		</dependency>
		<!--安全工具类模块-->
		<dependency>
			<groupId>com.joolun</groupId>
			<artifactId>joolun-common-security</artifactId>
		</dependency>
		<!--安全依赖获取上下文信息-->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security.oauth</groupId>
			<artifactId>spring-security-oauth2</artifactId>
		</dependency>
	</dependencies>
</project>
