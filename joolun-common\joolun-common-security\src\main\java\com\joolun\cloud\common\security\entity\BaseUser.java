package com.joolun.cloud.common.security.entity;

import lombok.Getter;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> 扩展用户信息
 */
public class BaseUser extends User {
	private static final long serialVersionUID = 1L;
	/**
	 * 用户ID
	 */
	@Getter
	private String id;
	/**
	 * 机构ID
	 */
	@Getter
	private String organId;

	/**
	 * -1、系统管理员；1、租户管理员；2、店铺管理员
	 */
	@Getter
	private String type;

	/**
	 * 租户ID
	 */
	@Getter
	private String tenantId;
	/**
	 * 店铺ID
	 */
	@Getter
	private String shopId;

	/**
	 * 平台系统管理员绑定的租户
	 */
	@Getter
	private List<String> tenantIds;
	/**
	 * Construct the <code>User</code> with the details required by
	 * {@link DaoAuthenticationProvider}.
	 *
	 * @param id                    用户ID
	 * @param organId                机构ID
	 * @param tenantId              租户ID
	 * @param username              the username presented to the
	 *                              <code>DaoAuthenticationProvider</code>
	 * @param password              the password that should be presented to the
	 *                              <code>DaoAuthenticationProvider</code>
	 * @param enabled               set to <code>true</code> if the user is enabled
	 * @param accountNonExpired     set to <code>true</code> if the account has not expired
	 * @param credentialsNonExpired set to <code>true</code> if the credentials have not
	 *                              expired
	 * @param accountNonLocked      set to <code>true</code> if the account is not locked
	 * @param authorities           the authorities that should be granted to the caller if they
	 *                              presented the correct username and password and the user is enabled. Not null.
	 * @throws IllegalArgumentException if a <code>null</code> value was passed either as
	 *                                  a parameter or as an element in the <code>GrantedAuthority</code> collection
	 */
	public BaseUser(String id, String organId, String type, String tenantId, String shopId, String username, String password, List<String> tenantIds,
					boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities) {
		super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
		this.id = id;
		this.organId = organId;
		this.type = type;
		this.tenantId = tenantId;
		this.shopId = shopId;
		this.tenantIds = tenantIds;
	}
}
