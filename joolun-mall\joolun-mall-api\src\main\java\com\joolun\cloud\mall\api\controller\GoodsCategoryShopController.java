/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.common.entity.GoodsCategoryShop;
import com.joolun.cloud.mall.api.service.GoodsCategoryShopService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 店铺商品分类表
 *
 * <AUTHOR>
 * @date 2020-08-26 11:51:54
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodscategoryshop")
@Api(value = "goodscategoryshop", tags = "店铺商品分类API")
public class GoodsCategoryShopController {

    private final GoodsCategoryShopService goodsCategoryShopService;

	/**
	 *  返回树形集合
	 * @return
	 */
	@ApiOperation(value = "返回树形集合")
	@GetMapping("/tree")
	public R getTree(GoodsCategoryShop goodsCategoryShop) {
		goodsCategoryShop.setEnable(CommonConstants.YES);
		return R.ok(goodsCategoryShopService.selectTree(goodsCategoryShop));
	}

}
