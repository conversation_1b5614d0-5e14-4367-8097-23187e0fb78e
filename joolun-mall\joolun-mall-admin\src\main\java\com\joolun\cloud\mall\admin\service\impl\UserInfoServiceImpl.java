/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.common.entity.UserInfo;
import com.joolun.cloud.mall.admin.mapper.UserInfoMapper;
import com.joolun.cloud.mall.admin.service.UserInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商城用户
 *
 * <AUTHOR>
 * @date 2019-12-04 11:09:55
 */
@Service
@AllArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

	@Override
	public IPage<UserInfo> page1(IPage<UserInfo> page, UserInfo userInfo) {
		return baseMapper.selectPage1(page,userInfo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updatePoints(String userId, Integer posts) {
		baseMapper.update(new UserInfo(), Wrappers.<UserInfo>lambdaUpdate()
				.eq(UserInfo::getId, userId)
				.setSql(" points_current = points_current + " + posts));
	}
}
