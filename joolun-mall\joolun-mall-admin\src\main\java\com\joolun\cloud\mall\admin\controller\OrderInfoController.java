/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.admin.service.OrderLogisticsService;
import com.joolun.cloud.mall.admin.service.UserInfoService;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.constant.MyReturnCode;
import com.joolun.cloud.mall.common.entity.OrderInfo;
import com.joolun.cloud.mall.admin.service.OrderInfoService;
import com.joolun.cloud.mall.common.entity.OrderItem;
import com.joolun.cloud.mall.common.entity.OrderLogistics;
import com.joolun.cloud.mall.common.enums.OrderInfoEnum;
import com.joolun.cloud.mall.common.feign.FeignOrderInfoApiService;
import com.joolun.cloud.mall.common.feign.FeignWxAppService;
import com.joolun.cloud.weixin.common.entity.WxApp;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商城订单
 *
 * <AUTHOR>
 * @date 2019-09-10 15:21:22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/orderinfo")
@Api(value = "orderinfo", tags = "商城订单管理")
public class OrderInfoController {

    private final OrderInfoService orderInfoService;
	private final UserInfoService userInfoService;
	private final FeignWxAppService feignWxAppService;
	private final OrderLogisticsService orderLogisticsService;
	private final FeignOrderInfoApiService feignOrderInfoApiService;

    /**
    * 分页查询
    * @param page 分页对象
    * @param orderInfo 商城订单
    * @return
    */
	@ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:orderinfo:index')")
    public R getOrderInfoPage(Page page, OrderInfo orderInfo) {
        return R.ok(orderInfoService.page1(page, Wrappers.query(orderInfo)));
    }

	/**
	 * 查询数量
	 * @param orderInfo
	 * @return
	 */
	@ApiOperation(value = "查询数量")
	@GetMapping("/count")
	public R getCount(OrderInfo orderInfo) {
		return R.ok(orderInfoService.count(Wrappers.query(orderInfo)));
	}

    /**
    * 通过id查询商城订单
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询商城订单")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:orderinfo:get')")
    public R getById(@PathVariable("id") String id){
		OrderInfo orderInfo = orderInfoService.getById(id);
		if(orderInfo.getAppType().equals(MallConstants.CLIENT_TYPE_MA) ||
				orderInfo.getAppType().equals(MallConstants.CLIENT_TYPE_H5_WX)){
			R<WxApp> r2 = feignWxAppService.getById(orderInfo.getAppId(), SecurityConstants.FROM_IN);
			orderInfo.setApp(r2.getData());
		}
		orderInfo.setUserInfo(userInfoService.getById(orderInfo.getUserId()));
		OrderLogistics orderLogistics = orderLogisticsService.getById(orderInfo.getLogisticsId());
		orderInfo.setOrderLogistics(orderLogistics);
        return R.ok(orderInfo);
    }

	/**
	 * 查询统计付款金额
	 * @param orderInfo
	 * @return R
	 */
	@ApiOperation(value = "查询统计付款金额")
	@GetMapping("/paymentprice/sum")
	@PreAuthorize("@ato.hasAuthority('mall:orderinfo:get')")
	public R sumPaymentPrice(OrderInfo orderInfo){
		return R.ok(orderInfoService.sumPaymentPrice(orderInfo));
	}

    /**
    * 新增商城订单
    * @param orderInfo 商城订单
    * @return R
    */
	@ApiOperation(value = "新增商城订单")
    @SysLog("新增商城订单")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:orderinfo:add')")
    public R save(@RequestBody OrderInfo orderInfo){
        return R.ok(orderInfoService.save(orderInfo));
    }

    /**
    * 修改商城订单
    * @param orderInfo 商城订单
    * @return R
    */
	@ApiOperation(value = "修改商城订单")
    @SysLog("修改商城订单")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:orderinfo:edit')")
    public R updateById(@RequestBody OrderInfo orderInfo){
        return R.ok(orderInfoService.updateById(orderInfo));
    }

    /**
    * 通过id删除商城订单
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id删除商城订单")
    @SysLog("删除商城订单")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:orderinfo:del')")
    public R removeById(@PathVariable String id){
        return R.ok(orderInfoService.removeById(id));
    }

	/**
	 * 修改商城订单价格
	 * @param orderItem
	 * @return R
	 */
	@ApiOperation(value = "修改商城订单价格")
	@SysLog("修改商城订单价格")
	@PutMapping("/editPrice")
	@PreAuthorize("@ato.hasAuthority('mall:orderinfo:edit')")
	public R editPrice(@RequestBody OrderItem orderItem){
		orderInfoService.editPrice(orderItem);
		return R.ok();
	}

	/**
	 * 取消商城订单
	 * @param id 商城订单
	 * @return R
	 */
	@ApiOperation(value = "取消商城订单")
	@SysLog("取消商城订单")
	@PutMapping("/cancel/{id}")
	@PreAuthorize("@ato.hasAuthority('mall:orderinfo:edit')")
	public R orderCancel(@PathVariable String id){
		return R.ok(feignOrderInfoApiService.orderCancelInside(id, SecurityConstants.FROM_IN));
	}

	/**
	 * 商城订单自提
	 * @param id 商城订单ID
	 * @return R
	 */
	@ApiOperation(value = "商城订单自提")
	@PutMapping("/takegoods/{id}")
	@PreAuthorize("@ato.hasAuthority('mall:orderinfo:edit')")
	public R takeGoods(@PathVariable String id){
		OrderInfo orderInfo = orderInfoService.getById(id);
		if(orderInfo == null){
			return R.failed(MyReturnCode.ERR_70005.getCode(), MyReturnCode.ERR_70005.getMsg());
		}
		if(!MallConstants.DELIVERY_WAY_2.equals(orderInfo.getDeliveryWay())
				&& !OrderInfoEnum.STATUS_1.getValue().equals(orderInfo.getStatus())){//只有待自提订单能确认收货
			return R.failed(MyReturnCode.ERR_70001.getCode(), MyReturnCode.ERR_70001.getMsg());
		}
		orderInfoService.orderReceive(orderInfo);
		return R.ok();
	}

	/**
	 * 查询统计
	 * @param orderInfo
	 * @return
	 */
	@ApiOperation(value = "查询统计")
	@GetMapping("/statistics")
	public R getStatistics(OrderInfo orderInfo) {
		//总数量
		int countTotal = orderInfoService.count(Wrappers.query(orderInfo));
		//今天数量
		LocalDate localDate = LocalDateTime.now().toLocalDate();
		int countToday = orderInfoService.count(Wrappers.query(orderInfo).between("create_time", LocalDateTime.of(localDate, LocalTime.MIN), LocalDateTime.of(localDate, LocalTime.MAX)));
		Map<String, Object> rs = new HashMap<>();
		rs.put("countTotal",countTotal);
		rs.put("countToday",countToday);
		return R.ok(rs);
	}

	/**
	 * 查询统计（根据某字段分组）
	 * @param orderInfo
	 * @return
	 */
	@ApiOperation(value = "查询统计（根据某字段分组）")
	@GetMapping("/statistics/{column}")
	public R getStatisticsByColumn(@PathVariable("column") String column, OrderInfo orderInfo) {
		List<Map<String, Object>> appTypeMapList= orderInfoService.listMaps(Wrappers.query(orderInfo)
				.select(column + " as name, count(*) as value")
				.groupBy(column)
				.orderByAsc(column));
		return R.ok(appTypeMapList);
	}
}
