/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CacheConstants;
import com.joolun.cloud.mall.common.entity.ShopInfo;
import com.joolun.cloud.mall.admin.mapper.ShopInfoMapper;
import com.joolun.cloud.mall.admin.service.ShopInfoService;
import com.joolun.cloud.weixin.common.entity.WxApp;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * 店铺表
 *
 * <AUTHOR>
 * @date 2020-05-15 13:35:18
 */
@Service
public class ShopInfoServiceImpl extends ServiceImpl<ShopInfoMapper, ShopInfo> implements ShopInfoService {

	@Override
	@Cacheable(value = CacheConstants.MALL_SHOP_INFO_CACHE, key = "#id", unless = "#result == null")
	public ShopInfo getById(Serializable id) {
		return baseMapper.selectById(id);
	}

	@Override
	@CacheEvict(value = CacheConstants.MALL_SHOP_INFO_CACHE, allEntries = true)
	public boolean updateById(ShopInfo entity) {
		baseMapper.updateById(entity);
		return Boolean.TRUE;
	}

	@Override
	@CacheEvict(value = CacheConstants.MALL_SHOP_INFO_CACHE, allEntries = true)
	public boolean removeById(Serializable id) {
		baseMapper.deleteById(id);
		return Boolean.TRUE;
	}
}
