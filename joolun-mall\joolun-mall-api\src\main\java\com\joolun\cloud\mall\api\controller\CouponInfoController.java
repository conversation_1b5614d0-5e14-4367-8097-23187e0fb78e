/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.annotation.ApiLogin;
import com.joolun.cloud.mall.api.service.CouponInfoService;
import com.joolun.cloud.mall.api.service.GoodsSpuService;
import com.joolun.cloud.mall.common.entity.CouponGoods;
import com.joolun.cloud.mall.common.entity.CouponInfo;
import com.joolun.cloud.mall.common.entity.CouponUser;
import com.joolun.cloud.mall.api.util.ThirdSessionHolder;
import com.joolun.cloud.mall.common.entity.GoodsSpu;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 电子券
 *
 * <AUTHOR>
 * @date 2019-12-14 11:30:58
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/couponinfo")
@Api(value = "couponinfo", tags = "电子券API")
public class CouponInfoController {

    private final CouponInfoService couponInfoService;
	private final GoodsSpuService goodsSpuService;

	/**
	 * 分页查询
	 * @param page 分页对象
	 * @param couponInfo 电子券
	 * @return
	 */
	@ApiOperation(value = "分页查询")
    @GetMapping("/page")
    public R getPage(Page page, CouponInfo couponInfo, CouponGoods cuponGoods) {
		CouponUser couponUser = new CouponUser();
		String userId = ThirdSessionHolder.getUserId();
		if(StrUtil.isNotBlank(userId)){
			couponUser.setUserId(userId);
		}
		if(StrUtil.isNotBlank(cuponGoods.getSpuId())){
			GoodsSpu goodsSpu = goodsSpuService.getById(cuponGoods.getSpuId());
			if(goodsSpu != null){
				couponInfo.setShopId(goodsSpu.getShopId());
			}
		}
        return R.ok(couponInfoService.page2(page, couponInfo, cuponGoods, couponUser));
    }

    /**
     * 通过id查询电子券
     * @param id
     * @return R
     */
	@ApiOperation(value = "通过id查询电子券")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        return R.ok(couponInfoService.getById2(id));
    }

}
