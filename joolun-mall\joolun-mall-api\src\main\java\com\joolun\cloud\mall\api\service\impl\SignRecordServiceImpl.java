/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.service.impl;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.api.mapper.PointsRecordMapper;
import com.joolun.cloud.mall.api.mapper.SignConfigMapper;
import com.joolun.cloud.mall.api.mapper.SignRecordMapper;
import com.joolun.cloud.mall.api.mapper.UserInfoMapper;
import com.joolun.cloud.mall.api.service.SignRecordService;
import com.joolun.cloud.mall.api.service.UserInfoService;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.entity.PointsRecord;
import com.joolun.cloud.mall.common.entity.SignConfig;
import com.joolun.cloud.mall.common.entity.SignRecord;
import com.joolun.cloud.mall.common.entity.UserInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 签到记录
 *
 * <AUTHOR>
 * @date 2021-01-13 15:29:30
 */
@Service
@AllArgsConstructor
public class SignRecordServiceImpl extends ServiceImpl<SignRecordMapper, SignRecord> implements SignRecordService {

	private final SignConfigMapper signConfigMapper;
	private final UserInfoService userInfoService;
	private final PointsRecordMapper pointsRecordMapper;

	@Override
	public SignRecord getSignRecord(String userId) {
		//获取用户的签到记录
		SignRecord signRecord = baseMapper.selectOne(Wrappers.<SignRecord>query().lambda()
				.eq(SignRecord::getUserId, userId));
		if(signRecord == null){//没有则新增
			signRecord = new SignRecord();
			signRecord.setUserId(userId);
			signRecord.setCumulateDays(0);
			signRecord.setContinuDays(0);
			baseMapper.insert(signRecord);
		}else if(signRecord.getUpdateTime() != null
				&& !signRecord.getUpdateTime().toLocalDate().equals(LocalDate.now())
				&& !signRecord.getUpdateTime().toLocalDate().equals(LocalDate.now().minusDays(1))){//判断当天是否是连续天数
			//最后签到时候不是今天和昨天，则清空连续天数
			signRecord.setContinuDays(0);
			baseMapper.updateById(signRecord);
		}
		if(signRecord.getUpdateTime() != null
				&& signRecord.getUpdateTime().toLocalDate().equals(LocalDate.now().minusDays(1))){//最后签到时间为昨天
			int count = signConfigMapper.selectCount(null);
			//昨天已经签满，则清空连续天数
			if(count == signRecord.getContinuDays()){
				signRecord.setContinuDays(0);
				baseMapper.updateById(signRecord);
			}
		}
		return signRecord;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SignConfig userSign(String userId) {
		//获取用户的签到记录
		SignRecord signRecord = this.getSignRecord(userId);
		//判断当天是否已经签到过
		if(signRecord.getUpdateTime() == null || !signRecord.getUpdateTime().toLocalDate().equals(LocalDate.now())){
			//获取签到配置
			Page page = new Page();
			page.setSearchCount(false);
			page.setSize(1);
			page.setCurrent(signRecord.getContinuDays()+1);
			List<OrderItem> orders = new ArrayList<>();
			OrderItem orderItem = new OrderItem();
			orderItem.setAsc(true);
			orderItem.setColumn("sort");
			page.setOrders(orders);
			List<SignConfig> listSignConfig = signConfigMapper.selectPage(page,null).getRecords();
			if(listSignConfig.size() <= 0){//已经签满，重头开始
				page.setCurrent(1);
				listSignConfig = signConfigMapper.selectPage(page,null).getRecords();
				if(listSignConfig.size() <= 0){
					throw new RuntimeException("请在后台设置签到信息");
				}else{
					//重置连续天数
					signRecord.setContinuDays(1);
				}
			}else{
				//连续天数+1
				signRecord.setContinuDays(signRecord.getContinuDays()+1);
			}
			SignConfig signConfig = listSignConfig.get(0);
			//累计天数+1
			signRecord.setCumulateDays(signRecord.getCumulateDays()+1);
			signRecord.setUpdateTime(LocalDateTime.now());
			baseMapper.updateById(signRecord);
			//更新用户积分
			userInfoService.updatePoints(userId,signConfig.getPosts());
			//新增积分记录
			PointsRecord pointsRecord = new PointsRecord();
			pointsRecord.setRecordType(MallConstants.POINTS_RECORD_TYPE_7);
			pointsRecord.setUserId(userId);
			pointsRecord.setAmount(signConfig.getPosts());
			pointsRecord.setDescription("签到积分");
			pointsRecordMapper.insert(pointsRecord);
			return signConfig;
		}else{
			return null;
		}
	}

}
