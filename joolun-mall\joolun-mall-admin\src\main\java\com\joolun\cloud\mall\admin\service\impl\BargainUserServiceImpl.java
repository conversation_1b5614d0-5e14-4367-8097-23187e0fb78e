/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.admin.mapper.BargainInfoMapper;
import com.joolun.cloud.mall.admin.service.BargainCutService;
import com.joolun.cloud.mall.common.entity.BargainUser;
import com.joolun.cloud.mall.admin.mapper.BargainUserMapper;
import com.joolun.cloud.mall.admin.service.BargainUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 砍价记录
 *
 * <AUTHOR>
 * @date 2019-12-30 11:53:14
 */
@Service
@AllArgsConstructor
public class BargainUserServiceImpl extends ServiceImpl<BargainUserMapper, BargainUser> implements BargainUserService {

	@Override
	public IPage<BargainUser> page1(IPage<BargainUser> page, BargainUser bargainUser) {
		return baseMapper.selectPage1(page, bargainUser);
	}

}
