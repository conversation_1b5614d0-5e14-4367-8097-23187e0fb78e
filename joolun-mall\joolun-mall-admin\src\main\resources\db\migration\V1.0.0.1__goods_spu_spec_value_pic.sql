-- Create goods_spu_spec_value_pic table
CREATE TABLE IF NOT EXISTS `goods_spu_spec_value_pic` (
  `id` varchar(32) NOT NULL COMMENT 'PK',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '所属租户',
  `spu_id` varchar(32) NOT NULL COMMENT 'SPU ID',
  `spec_id` varchar(32) NOT NULL COMMENT '规格ID',
  `spec_value_id` varchar(32) NOT NULL COMMENT '规格值ID',
  `pic` varchar(500) DEFAULT NULL COMMENT '规格值图片',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_spu_spec_value` (`spu_id`,`spec_value_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SPU规格值图片'; 