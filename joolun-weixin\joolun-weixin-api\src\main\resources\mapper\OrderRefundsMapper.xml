<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.joolun.cloud.weixin.api.mapper.OrderRefundsMapper">

	<resultMap id="orderRefundsMap" type="com.joolun.cloud.weixin.common.entity.OrderRefunds">
		<id property="id" column="id"/>
		<result property="tenantId" column="tenant_id"/>
		<result property="shopId" column="shop_id"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="createId" column="create_id"/>
		<result property="orderId" column="order_id"/>
		<result property="orderItemId" column="order_item_id"/>
		<result property="status" column="status"/>
		<result property="refundAmount" column="refund_amount"/>
		<result property="refundTradeNo" column="refund_trade_no"/>
		<result property="refundReson" column="refund_reson"/>
		<result property="refuseRefundReson" column="refuse_refund_reson"/>
	</resultMap>

	<sql id="orderRefundsSql">
		obj.`id`,
		obj.`tenant_id`,
		obj.`shop_id`,
		obj.`del_flag`,
		obj.`create_time`,
		obj.`update_time`,
		obj.`create_id`,
		obj.`order_id`,
		obj.`order_item_id`,
		obj.`status`,
		obj.`refund_amount`,
		obj.`refund_trade_no`,
		obj.`refund_reson`,
		obj.`refuse_refund_reson`
	</sql>

	<select id="selectList2" resultMap="orderRefundsMap">
		SELECT
		<include refid="orderRefundsSql"/>
		FROM order_refunds obj
		<where>
			<if test="query.orderId != null">
				AND obj.`order_id` = #{query.orderId}
			</if>
			<if test="query.orderItemId != null">
				AND obj.`order_item_id` = #{query.orderItemId}
			</if>
		</where>
		ORDER BY obj.`create_time` desc
	</select>

</mapper>
