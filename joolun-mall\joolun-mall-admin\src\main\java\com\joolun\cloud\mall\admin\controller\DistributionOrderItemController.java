/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.DistributionOrderItem;
import com.joolun.cloud.mall.admin.service.DistributionOrderItemService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 分销订单明细
 *
 * <AUTHOR>
 * @date 2021-05-06 14:51:44
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/distributionorderitem")
@Api(value = "distributionorderitem", tags = "分销订单明细管理")
public class DistributionOrderItemController {

    private final DistributionOrderItemService distributionOrderItemService;

    /**
     * 分销订单明细分页列表
     * @param page 分页对象
     * @param distributionOrderItem 分销订单明细
     * @return
     */
    @ApiOperation(value = "分销订单明细分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:distributionorderitem:index')")
    public R getPage(Page page, DistributionOrderItem distributionOrderItem) {
        return R.ok(distributionOrderItemService.page(page, Wrappers.query(distributionOrderItem)));
    }

    /**
     * 分销订单明细查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销订单明细查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionorderitem:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(distributionOrderItemService.getById(id));
    }

    /**
     * 分销订单明细新增
     * @param distributionOrderItem 分销订单明细
     * @return R
     */
    @ApiOperation(value = "分销订单明细新增")
    @SysLog("新增分销订单明细")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionorderitem:add')")
    public R save(@RequestBody DistributionOrderItem distributionOrderItem) {
        return R.ok(distributionOrderItemService.save(distributionOrderItem));
    }

    /**
     * 分销订单明细修改
     * @param distributionOrderItem 分销订单明细
     * @return R
     */
    @ApiOperation(value = "分销订单明细修改")
    @SysLog("修改分销订单明细")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionorderitem:edit')")
    public R updateById(@RequestBody DistributionOrderItem distributionOrderItem) {
        return R.ok(distributionOrderItemService.updateById(distributionOrderItem));
    }

    /**
     * 分销订单明细删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销订单明细删除")
    @SysLog("删除分销订单明细")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionorderitem:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(distributionOrderItemService.removeById(id));
    }

}
