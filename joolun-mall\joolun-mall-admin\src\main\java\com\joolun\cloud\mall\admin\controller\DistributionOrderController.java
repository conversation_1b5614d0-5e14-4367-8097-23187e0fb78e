/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.common.entity.DistributionOrder;
import com.joolun.cloud.mall.admin.service.DistributionOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 分销订单
 *
 * <AUTHOR>
 * @date 2021-04-30 10:26:30
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/distributionorder")
@Api(value = "distributionorder", tags = "分销订单管理")
public class DistributionOrderController {

    private final DistributionOrderService distributionOrderService;

    /**
     * 分销订单分页列表
     * @param page 分页对象
     * @param distributionOrder 分销订单
     * @return
     */
    @ApiOperation(value = "分销订单分页列表")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:distributionorder:index','mall:distributionuser:index')")
    public R getPage(Page page, DistributionOrder distributionOrder) {
        return R.ok(distributionOrderService.page1(page, distributionOrder));
    }

    /**
     * 分销订单查询
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销订单查询")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionorder:get')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(distributionOrderService.getById(id));
    }

    /**
     * 分销订单新增
     * @param distributionOrder 分销订单
     * @return R
     */
    @ApiOperation(value = "分销订单新增")
    @SysLog("新增分销订单")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionorder:add')")
    public R save(@RequestBody DistributionOrder distributionOrder) {
        return R.ok(distributionOrderService.save(distributionOrder));
    }

    /**
     * 分销订单修改
     * @param distributionOrder 分销订单
     * @return R
     */
    @ApiOperation(value = "分销订单修改")
    @SysLog("修改分销订单")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:distributionorder:edit','mall:distributionuser:get')")
    public R updateById(@RequestBody DistributionOrder distributionOrder) {
        return R.ok(distributionOrderService.updateById(distributionOrder));
    }

    /**
     * 分销订单删除
     * @param id
     * @return R
     */
    @ApiOperation(value = "分销订单删除")
    @SysLog("删除分销订单")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:distributionorder:del')")
    public R removeById(@PathVariable String id) {
        return R.ok(distributionOrderService.removeById(id));
    }

}
