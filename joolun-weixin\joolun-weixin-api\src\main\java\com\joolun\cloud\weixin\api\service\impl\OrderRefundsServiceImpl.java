/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.weixin.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.weixin.api.mapper.OrderRefundsMapper;
import com.joolun.cloud.weixin.api.service.OrderInfoService;
import com.joolun.cloud.weixin.api.service.OrderItemService;
import com.joolun.cloud.weixin.api.service.OrderRefundsService;
import com.joolun.cloud.weixin.api.util.ThirdSessionHolder;
import com.joolun.cloud.weixin.common.entity.OrderInfo;
import com.joolun.cloud.weixin.common.entity.OrderItem;
import com.joolun.cloud.weixin.common.entity.OrderRefunds;
import com.joolun.cloud.weixin.common.enums.OrderInfoEnum;
import com.joolun.cloud.weixin.common.enums.OrderItemEnum;
import com.joolun.cloud.weixin.common.enums.OrderRefundsEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 退款详情
 *
 * <AUTHOR>
 * @date 2019-11-14 16:35:25
 */
@Service
@AllArgsConstructor
public class OrderRefundsServiceImpl extends ServiceImpl<OrderRefundsMapper, OrderRefunds> implements OrderRefundsService {

	private final OrderInfoService orderInfoService;
	private final OrderItemService orderItemService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveRefunds(OrderRefunds entity) {
		OrderItem orderItem = orderItemService.getById(entity.getOrderItemId());
		if(StrUtil.isNotBlank(entity.getStatus())
				&& orderItem != null
				&& CommonConstants.NO.equals(orderItem.getIsRefund())
		 		&& OrderItemEnum.STATUS_0.getValue().equals(orderItem.getStatus())){//只有未退款的订单才能发起退款
			//修改订单详情状态为退款中
			orderItem.setStatus(entity.getStatus());
			orderItem.setIsRefund(CommonConstants.NO);
			orderItemService.updateById(orderItem);
			//新增退款记录
			entity.setTenantId(ThirdSessionHolder.getThirdSession().getTenantId());
			entity.setShopId(orderItem.getShopId());
			entity.setOrderId(orderItem.getOrderId());
			entity.setOrderItemId(orderItem.getId());
			entity.setRefundAmount(orderItem.getPaymentPrice());
			baseMapper.insert(entity);
		}
		return Boolean.TRUE;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void doOrderRefundsResult(OrderRefunds orderRefunds) {
		//修改订单详情退款状态为已退款
		OrderItem orderItem = orderItemService.getById(orderRefunds.getOrderItemId());
		if ((OrderRefundsEnum.STATUS_11.getValue().equals(orderRefunds.getStatus()) || OrderRefundsEnum.STATUS_211.getValue().equals(orderRefunds.getStatus()))
				&& CommonConstants.NO.equals(orderItem.getIsRefund())) {
			orderItem.setIsRefund(CommonConstants.YES);
			String status = "";
			if (OrderRefundsEnum.STATUS_11.getValue().equals(orderRefunds.getStatus())) {
				status = OrderItemEnum.STATUS_3.getValue();
			}
			if (OrderRefundsEnum.STATUS_211.getValue().equals(orderRefunds.getStatus())) {
				status = OrderItemEnum.STATUS_4.getValue();
			}
			orderItem.setStatus(status);
			orderItemService.updateById(orderItem);

			OrderInfo orderInfo ;
			List<OrderItem> listOrderItem = orderItemService.list(Wrappers.<OrderItem>query().lambda()
					.eq(OrderItem::getOrderId, orderItem.getOrderId()));
			List<OrderItem> listOrderItem2 = listOrderItem.stream()
					.filter(obj -> !obj.getId().equals(orderRefunds.getOrderItemId()) && CommonConstants.NO.equals(obj.getIsRefund())).collect(Collectors.toList());
			//如果订单下面所有订单详情都退款了，则取消订单
			if (listOrderItem2.size() <= 0) {
				orderInfo = new OrderInfo();
				orderInfo.setId(orderItem.getOrderId());
				orderInfo.setStatus(OrderInfoEnum.STATUS_5.getValue());
				orderInfoService.updateById(orderInfo);
			}
		}
	}
}
