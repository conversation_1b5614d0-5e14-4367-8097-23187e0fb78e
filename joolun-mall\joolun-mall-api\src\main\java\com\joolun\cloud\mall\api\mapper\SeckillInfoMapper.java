/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joolun.cloud.mall.common.entity.SeckillHallInfo;
import com.joolun.cloud.mall.common.entity.SeckillInfo;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * 秒杀商品
 *
 * <AUTHOR>
 * @date 2020-08-12 16:16:45
 */
public interface SeckillInfoMapper extends BaseMapper<SeckillInfo> {

	IPage<SeckillInfo> selectPage2(IPage<SeckillInfo> page, @Param("query") SeckillInfo seckillInfo, @Param("query2") SeckillHallInfo seckillHallInfo);

	SeckillInfo selectById2(Serializable id);
}
