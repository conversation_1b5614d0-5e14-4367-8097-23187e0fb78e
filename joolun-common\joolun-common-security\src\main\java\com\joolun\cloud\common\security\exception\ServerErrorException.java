package com.joolun.cloud.common.security.exception;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.joolun.cloud.common.security.component.BaseAuth2ExceptionSerializer;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
@JsonSerialize(using = BaseAuth2ExceptionSerializer.class)
public class ServerErrorException extends BaseAuth2Exception {

	public ServerErrorException(String msg, Throwable t) {
		super(msg);
	}

	@Override
	public String getOAuth2ErrorCode() {
		return "server_error";
	}

	@Override
	public int getHttpErrorCode() {
		return HttpStatus.INTERNAL_SERVER_ERROR.value();
	}

}
