/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.admin.service.SeckillHallInfoService;
import com.joolun.cloud.mall.common.entity.SeckillHallInfo;
import com.joolun.cloud.mall.common.entity.SeckillInfo;
import com.joolun.cloud.mall.admin.mapper.SeckillInfoMapper;
import com.joolun.cloud.mall.admin.service.SeckillInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;

/**
 * 秒杀商品
 *
 * <AUTHOR>
 * @date 2020-08-12 11:03:50
 */
@Service
@AllArgsConstructor
public class SeckillInfoServiceImpl extends ServiceImpl<SeckillInfoMapper, SeckillInfo> implements SeckillInfoService {

	private final SeckillHallInfoService seckillHallInfoService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeById(Serializable id) {
		//先删除关联商品
		seckillHallInfoService.remove(Wrappers.<SeckillHallInfo>lambdaQuery()
				.eq(SeckillHallInfo::getSeckillInfoId,id));
		return super.removeById(id);
	}
}
