/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joolun.cloud.mall.common.entity.DistributionOrder;

import java.math.BigDecimal;

/**
 * 分销订单
 *
 * <AUTHOR>
 * @date 2021-04-30 10:26:30
 */
public interface DistributionOrderService extends IService<DistributionOrder> {

	IPage<DistributionOrder> page1(IPage<DistributionOrder> page, DistributionOrder distributionOrder);

	/**
	 * 获取某个分销员的冻结金额
	 * @param distributionUserId
	 * @return
	 */
	BigDecimal getDistributionUserFrozenAmount(String distributionUserId);
	/**
	 * 统一更新操作佣金金额
	 * @param id
	 * @param commission 金额变量。+加，-减
	 */
	void updateCommission(String id, BigDecimal commission);
}
