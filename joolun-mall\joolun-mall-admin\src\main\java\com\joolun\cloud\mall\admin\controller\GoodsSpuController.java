/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.controller;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.common.log.annotation.SysLog;
import com.joolun.cloud.mall.admin.service.*;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.entity.*;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.Api;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * spu商品
 *
 * <AUTHOR>
 * @date 2019-08-12 16:25:10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goodsspu")
@Api(value = "goodsspu", tags = "spu商品管理")
public class GoodsSpuController {

    private final GoodsSpuService goodsSpuService;
	private final FreightTemplatService freightTemplatService;
	private final BargainInfoService bargainInfoService;
	private final GrouponInfoService grouponInfoService;
	private final SeckillInfoService seckillInfoService;

    /**
    * 分页查询
    * @param page 分页对象
    * @param goodsSpu spu商品
    * @return
    */
	@ApiOperation(value = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:index')")
    public R getGoodsSpuPage(Page page, GoodsSpu goodsSpu) {
		return R.ok(goodsSpuService.page1(page, goodsSpu));
    }

	/**
	 * list查询
	 * @param goodsSpu
	 * @return
	 */
	@ApiOperation(value = "list查询")
	@GetMapping("/list")
	public List<GoodsSpu> getList(GoodsSpu goodsSpu) {
		return goodsSpuService.list(Wrappers.query(goodsSpu).lambda()
						.select(GoodsSpu::getId,
								GoodsSpu::getName,
								GoodsSpu::getShopId)
				);
	}

	/**
	 * 查询数量
	 * @param goodsSpu
	 * @return
	 */
	@ApiOperation(value = "查询数量")
	@GetMapping("/count")
	public R getCount(GoodsSpu goodsSpu) {
		return R.ok(goodsSpuService.count(Wrappers.query(goodsSpu)));
	}

    /**
    * 通过id查询spu商品
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id查询spu商品")
    @GetMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:get')")
    public R getById(@PathVariable("id") String id){
        return R.ok(goodsSpuService.getById1(id));
    }

    /**
    * 新增spu商品
    * @param goodsSpu spu商品
    * @return R
    */
	@ApiOperation(value = "新增spu商品")
    @SysLog("新增spu商品")
    @PostMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:add')")
    public R save(@RequestBody GoodsSpu goodsSpu){
//		TODO：审核状态卖的时候要默认0
		goodsSpu.setVerifyStatus(MallConstants.GOODS_STATUS_1);
		FreightTemplat freightTemplat = freightTemplatService.getById(goodsSpu.getFreightTemplatId());
		if(!goodsSpu.getShopId().equals(freightTemplat.getShopId())){
			return R.failed("请选择商品所属店铺下的运费模板");
		}
        return R.ok(goodsSpuService.save1(goodsSpu));
    }

    /**
    * 修改spu商品
    * @param goodsSpu spu商品
    * @return R
    */
	@ApiOperation(value = "修改spu商品")
    @SysLog("修改spu商品")
    @PutMapping
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:edit')")
    public R updateById(@RequestBody GoodsSpu goodsSpu){
//		TODO：审核状态卖的时候要默认0
		goodsSpu.setVerifyStatus(MallConstants.GOODS_STATUS_1);
		FreightTemplat freightTemplat = freightTemplatService.getById(goodsSpu.getFreightTemplatId());
		if(!goodsSpu.getShopId().equals(freightTemplat.getShopId())){
			return R.failed("请选择商品所属店铺下的运费模板");
		}
        return R.ok(goodsSpuService.updateById1(goodsSpu));
    }

	/**
	 * 商品上下架操作
	 * @param shelf
	 * @param ids
	 * @return R
	 */
	@ApiOperation(value = "商品上下架操作")
	@SysLog("商品上下架操作")
	@PutMapping("/shelf")
	@PreAuthorize("@ato.hasAuthority('mall:goodsspu:edit')")
	public R shelf(@RequestParam(value = "shelf") String shelf, @RequestParam(value = "ids") String ids){
		GoodsSpu goodsSpu = new GoodsSpu();
		goodsSpu.setShelf(shelf);
		return R.ok(goodsSpuService.update(goodsSpu,Wrappers.<GoodsSpu>lambdaQuery()
				.in(GoodsSpu::getId, Convert.toList(ids))));
	}

    /**
    * 通过id删除spu商品
    * @param id
    * @return R
    */
	@ApiOperation(value = "通过id删除spu商品")
    @SysLog("删除spu商品")
    @DeleteMapping("/{id}")
    @PreAuthorize("@ato.hasAuthority('mall:goodsspu:del')")
    public R removeById(@PathVariable String id){
		//在删除商品的时候加上判断，如该商品有参与营销活动就不能删除
		//砍价
		BargainInfo bargainInfo = new BargainInfo();
		bargainInfo.setSpuId(id);
		List<BargainInfo> listBargainInfo = bargainInfoService.list(Wrappers.query(bargainInfo));
		if(listBargainInfo != null && listBargainInfo.size() > 0){
			return R.failed("该商品有参与砍价活动，不能删除");
		}
		//拼团
		GrouponInfo grouponInfo = new GrouponInfo();
		grouponInfo.setSpuId(id);
		List<GrouponInfo> listGrouponInfo = grouponInfoService.list(Wrappers.query(grouponInfo));
		if(listGrouponInfo != null && listGrouponInfo.size() > 0){
			return R.failed("该商品有参与拼团活动，不能删除");
		}
		//秒杀
		SeckillInfo seckillInfo = new SeckillInfo();
		seckillInfo.setSpuId(id);
		List<SeckillInfo> listSeckillInfo = seckillInfoService.list(Wrappers.query(seckillInfo));
		if(listSeckillInfo != null && listSeckillInfo.size() > 0){
			return R.failed("该商品有参与秒杀活动，不能删除");
		}
        return R.ok(goodsSpuService.removeById(id));
    }

	/**
	 * 商品审核
	 * @param verifyStatus
	 * @param ids
	 * @return R
	 */
	@ApiOperation(value = "商品审核")
	@SysLog("商品审核")
	@PutMapping("/verify")
	@PreAuthorize("@ato.hasAuthority('mall:goodsspu:verify')")
	public R verify(@RequestParam(value = "verifyStatus") String verifyStatus, @RequestParam(value = "verifyDetail",required=false) String verifyDetail, @RequestParam(value = "ids") String ids){
		GoodsSpu goodsSpu = new GoodsSpu();
		goodsSpu.setVerifyStatus(verifyStatus);
		goodsSpu.setVerifyDetail(verifyDetail);
		return R.ok(goodsSpuService.update(goodsSpu,Wrappers.<GoodsSpu>lambdaQuery()
				.in(GoodsSpu::getId, Convert.toList(ids))));
	}

	/**
	 * 查询统计
	 * @param goodsSpu
	 * @return
	 */
	@ApiOperation(value = "查询统计")
	@GetMapping("/statistics")
	public R getStatistics(GoodsSpu goodsSpu) {
		//总数量
		int countTotal = goodsSpuService.count(Wrappers.query(goodsSpu));
		//今天数量
		LocalDate localDate = LocalDateTime.now().toLocalDate();
		int countToday = goodsSpuService.count(Wrappers.query(goodsSpu).between("create_time", LocalDateTime.of(localDate, LocalTime.MIN), LocalDateTime.of(localDate, LocalTime.MAX)));
		Map<String, Object> rs = new HashMap<>();
		rs.put("countTotal",countTotal);
		rs.put("countToday",countToday);
		return R.ok(rs);
	}
}
