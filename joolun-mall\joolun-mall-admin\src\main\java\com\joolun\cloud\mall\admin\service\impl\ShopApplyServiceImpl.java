/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.common.core.constant.CommonConstants;
import com.joolun.cloud.common.core.constant.SecurityConstants;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.admin.service.ShopInfoService;
import com.joolun.cloud.mall.common.constant.MallConstants;
import com.joolun.cloud.mall.common.entity.ShopApply;
import com.joolun.cloud.mall.admin.mapper.ShopApplyMapper;
import com.joolun.cloud.mall.admin.service.ShopApplyService;
import com.joolun.cloud.mall.common.entity.ShopInfo;
import com.joolun.cloud.upms.common.entity.SysUser;
import com.joolun.cloud.upms.common.feign.FeignShopUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 店铺入驻申请表
 *
 * <AUTHOR>
 * @date 2020-10-28 10:39:58
 */
@Service
@AllArgsConstructor
public class ShopApplyServiceImpl extends ServiceImpl<ShopApplyMapper, ShopApply> implements ShopApplyService {

	private final ShopInfoService shopInfoService;
	private final FeignShopUserService feignShopUserService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean verify(ShopApply shopApply) {
		String status = shopApply.getStatus();
		String applyDetail = shopApply.getApplyDetail();
		shopApply = baseMapper.selectById(shopApply.getId());
		if(MallConstants.SHOPAPPLY_STATUS_0.equals(shopApply.getStatus())){//只有审核中状态的数据能操作
			shopApply.setStatus(status);
			shopApply.setApplyDetail(applyDetail);
			if(MallConstants.SHOPAPPLY_STATUS_1.equals(status)){//如果审核通过，则需要添加店铺和店员
				//添加店铺
				ShopInfo shopInfo = new ShopInfo();
				BeanUtil.copyProperties(shopApply,shopInfo);
				shopInfo.setTenantId(null);
				shopInfo.setEnable(CommonConstants.YES);
				shopInfoService.save(shopInfo);
				shopApply.setShopId(shopInfo.getId());
				//更新状态
				baseMapper.updateById(shopApply);
				SysUser sysUser = new SysUser();
				sysUser.setShopId(shopInfo.getId());
				sysUser.setUsername(shopApply.getUserUsername());
				sysUser.setPassword(shopApply.getUserPassword());
				sysUser.setPhone(shopApply.getUserPhone());
				sysUser.setEmail(shopApply.getUserEmail());
				sysUser.setAvatar(shopApply.getUserAvatar());
				sysUser.setNickName(shopApply.getUserNickname());
				//新增店员
				R r = feignShopUserService.save(sysUser, SecurityConstants.FROM_IN);
				if(!r.isOk()){
					throw new RuntimeException(r.getMsg());
				}
			}else{
				baseMapper.updateById(shopApply);
			}
		}
		return Boolean.TRUE;
	}
}
