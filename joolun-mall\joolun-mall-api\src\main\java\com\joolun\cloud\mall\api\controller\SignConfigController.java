/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.mall.api.service.SignConfigService;
import com.joolun.cloud.mall.common.entity.SignConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 签到设置
 *
 * <AUTHOR>
 * @date 2021-01-13 14:30:42
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/signconfig")
@Api(value = "signconfig", tags = "签到设置API")
public class SignConfigController {

    private final SignConfigService signConfigService;

    /**
     * 分页列表
     * @param page 分页对象
     * @param signConfig 签到设置
     * @return
     */
    @ApiOperation(value = "分页列表")
    @GetMapping("/page")
    public R getPage(Page page, SignConfig signConfig) {
        return R.ok(signConfigService.page(page, Wrappers.query(signConfig)));
    }

}
