/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.mall.api.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joolun.cloud.mall.common.entity.UserFootprint;
import com.joolun.cloud.mall.api.mapper.UserFootprintMapper;
import com.joolun.cloud.mall.api.service.UserFootprintService;
import org.springframework.stereotype.Service;

/**
 * 用户足迹
 *
 * <AUTHOR>
 * @date 2020-12-24 22:09:03
 */
@Service
public class UserFootprintServiceImpl extends ServiceImpl<UserFootprintMapper, UserFootprint> implements UserFootprintService {
	@Override
	public IPage<UserFootprint> page2(IPage<UserFootprint> page, UserFootprint userFootprint) {
		return baseMapper.selectPage2(page,userFootprint);
	}
}
