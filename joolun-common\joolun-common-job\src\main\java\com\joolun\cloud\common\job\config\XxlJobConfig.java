/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.joolun.cloud.common.job.config;

import com.joolun.cloud.common.job.properties.XxlJobProperties;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * xxl 初始化
 */
@Slf4j
@Configuration
@AllArgsConstructor
public class XxlJobConfig {

	private final XxlJobProperties xxlJobProperties;
	@Bean
	public XxlJobSpringExecutor xxlJobSpringExecutor() {
		if(xxlJobProperties.getEnabled()){
			XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
			xxlJobSpringExecutor.setPort(xxlJobProperties.getExecutor().getPort());
			xxlJobSpringExecutor.setAdminAddresses(xxlJobProperties.getAdmin().getAddresses());
			xxlJobSpringExecutor.setAppname(xxlJobProperties.getExecutor().getAppname());
			xxlJobSpringExecutor.setAddress(xxlJobProperties.getExecutor().getAddress());
			xxlJobSpringExecutor.setIp(xxlJobProperties.getExecutor().getIp());
			xxlJobSpringExecutor.setAccessToken(xxlJobProperties.getExecutor().getAccessToken());
			xxlJobSpringExecutor.setLogPath(xxlJobProperties.getExecutor().getLogPath());
			xxlJobSpringExecutor.setLogRetentionDays(xxlJobProperties.getExecutor().getLogRetentionDays());
			return xxlJobSpringExecutor;
		}
		return null;
	}
}
